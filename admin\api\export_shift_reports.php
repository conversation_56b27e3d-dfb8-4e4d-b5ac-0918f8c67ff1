<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/auth.php';

// معالجة الفلاتر
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-7 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$client_filter = $_GET['client_id'] ?? '';
$employee_filter = $_GET['employee_id'] ?? '';

// بناء استعلام التقارير
$reports_query = "
    SELECT 
        sa.attendance_id,
        sa.shift_id,
        sa.employee_id,
        sa.check_in_time,
        sa.check_out_time,
        sa.total_hours as actual_hours_worked,
        s.shift_name,
        s.shift_date,
        s.start_time,
        s.end_time,
        e.name as employee_name,
        e.role as employee_role,
        c.business_name,
        COALESCE(sr.total_sessions, 0) as total_sessions,
        COALESCE(sr.total_sales, 0) as total_sales,
        COALESCE(sr.total_revenue, 0) as total_revenue,
        COALESCE(sr.customers_served, 0) as customers_served,
        COALESCE(sr.products_sold, 0) as products_sold,
        COALESCE(sr.performance_score, 0) as performance_score
    FROM shift_attendance sa
    JOIN shifts s ON sa.shift_id = s.shift_id
    JOIN employees e ON sa.employee_id = e.id
    JOIN clients c ON s.client_id = c.client_id
    LEFT JOIN shift_reports sr ON sa.shift_id = sr.shift_id AND sa.employee_id = sr.employee_id
    WHERE s.shift_date BETWEEN ? AND ?
";

$params = [$date_from, $date_to];

if ($client_filter) {
    $reports_query .= " AND s.client_id = ?";
    $params[] = $client_filter;
}

if ($employee_filter) {
    $reports_query .= " AND sa.employee_id = ?";
    $params[] = $employee_filter;
}

$reports_query .= " ORDER BY s.shift_date DESC, sa.check_in_time DESC";

$reports_stmt = $pdo->prepare($reports_query);
$reports_stmt->execute($params);
$reports = $reports_stmt->fetchAll();

// تحديد نوع التصدير
$export_type = $_GET['export'] ?? 'csv';

if ($export_type === 'excel') {
    // تصدير Excel
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="shift_reports_' . date('Y-m-d') . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    echo '<table border="1">';
    echo '<tr>';
    echo '<th>التاريخ</th>';
    echo '<th>الشيفت</th>';
    echo '<th>الموظف</th>';
    echo '<th>المحل</th>';
    echo '<th>وقت الحضور</th>';
    echo '<th>وقت الانصراف</th>';
    echo '<th>ساعات العمل</th>';
    echo '<th>الجلسات</th>';
    echo '<th>الإيرادات</th>';
    echo '<th>المبيعات</th>';
    echo '<th>الأداء</th>';
    echo '</tr>';
    
    foreach ($reports as $report) {
        echo '<tr>';
        echo '<td>' . date('Y-m-d', strtotime($report['shift_date'])) . '</td>';
        echo '<td>' . htmlspecialchars($report['shift_name']) . '</td>';
        echo '<td>' . htmlspecialchars($report['employee_name']) . '</td>';
        echo '<td>' . htmlspecialchars($report['business_name']) . '</td>';
        echo '<td>' . ($report['check_in_time'] ? date('H:i:s', strtotime($report['check_in_time'])) : '-') . '</td>';
        echo '<td>' . ($report['check_out_time'] ? date('H:i:s', strtotime($report['check_out_time'])) : '-') . '</td>';
        echo '<td>' . ($report['actual_hours_worked'] ?? 0) . '</td>';
        echo '<td>' . $report['total_sessions'] . '</td>';
        echo '<td>' . number_format($report['total_revenue'], 2) . '</td>';
        echo '<td>' . number_format($report['total_sales'], 2) . '</td>';
        echo '<td>' . number_format($report['performance_score'], 1) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    
} else {
    // تصدير CSV
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="shift_reports_' . date('Y-m-d') . '.csv"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // إضافة BOM للدعم العربي
    echo "\xEF\xBB\xBF";
    
    $output = fopen('php://output', 'w');
    
    // كتابة العناوين
    fputcsv($output, [
        'التاريخ',
        'الشيفت', 
        'الموظف',
        'المحل',
        'وقت الحضور',
        'وقت الانصراف',
        'ساعات العمل',
        'الجلسات',
        'الإيرادات',
        'المبيعات',
        'الأداء'
    ]);
    
    // كتابة البيانات
    foreach ($reports as $report) {
        fputcsv($output, [
            date('Y-m-d', strtotime($report['shift_date'])),
            $report['shift_name'],
            $report['employee_name'],
            $report['business_name'],
            $report['check_in_time'] ? date('H:i:s', strtotime($report['check_in_time'])) : '-',
            $report['check_out_time'] ? date('H:i:s', strtotime($report['check_out_time'])) : '-',
            $report['actual_hours_worked'] ?? 0,
            $report['total_sessions'],
            number_format($report['total_revenue'], 2),
            number_format($report['total_sales'], 2),
            number_format($report['performance_score'], 1)
        ]);
    }
    
    fclose($output);
}
?>
