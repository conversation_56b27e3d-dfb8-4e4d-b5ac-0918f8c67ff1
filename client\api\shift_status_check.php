<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

header('Content-Type: application/json');

// التحقق من تسجيل دخول الموظف
if (!isset($_SESSION['employee_id'])) {
    echo json_encode(['success' => false, 'error' => 'غير مصرح بالوصول']);
    exit;
}

$employee_id = $_SESSION['employee_id'];

try {
    // البحث عن الشيفت النشط
    $stmt = $pdo->prepare("
        SELECT 
            sa.attendance_id,
            sa.shift_id,
            sa.check_in_time,
            s.shift_name,
            s.start_time,
            s.end_time,
            TIMESTAMPDIFF(MINUTE, sa.check_in_time, NOW()) as minutes_worked
        FROM shift_attendance sa 
        JOIN shifts s ON sa.shift_id = s.shift_id 
        WHERE sa.employee_id = ? 
        AND sa.check_out_time IS NULL 
        AND s.status = 'active'
        LIMIT 1
    ");
    $stmt->execute([$employee_id]);
    $active_shift = $stmt->fetch();
    
    if ($active_shift) {
        echo json_encode([
            'success' => true,
            'active_shift' => [
                'shift_id' => $active_shift['shift_id'],
                'shift_name' => $active_shift['shift_name'],
                'start_time' => $active_shift['start_time'],
                'end_time' => $active_shift['end_time'],
                'check_in_time' => $active_shift['check_in_time'],
                'minutes_worked' => (int)$active_shift['minutes_worked']
            ]
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'active_shift' => null
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ في جلب حالة الشيفت: ' . $e->getMessage()
    ]);
}
?>
