<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/auth.php';

header('Content-Type: application/json');

if (!isset($_GET['client_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'معرف المحل مطلوب']);
    exit;
}

$client_id = intval($_GET['client_id']);

try {
    $stmt = $pdo->prepare("
        SELECT id, name, role 
        FROM employees 
        WHERE client_id = ? AND is_active = 1 
        ORDER BY name ASC
    ");
    $stmt->execute([$client_id]);
    $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($employees);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'حدث خطأ في جلب الموظفين: ' . $e->getMessage()]);
}
?>
