<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// دالة إرسال إشعارات الشيفتات
function sendShiftNotification($pdo, $client_id, $type, $shift_id, $employee_id, $shift_name) {
    try {
        // جلب معلومات الموظف
        $stmt = $pdo->prepare("SELECT name FROM employees WHERE id = ?");
        $stmt->execute([$employee_id]);
        $employee = $stmt->fetch();
        $employee_name = $employee ? $employee['name'] : 'موظف غير معروف';

        // تحديد نوع الإشعار والرسالة
        switch ($type) {
            case 'shift_start':
                $title = 'بدء شيفت جديد';
                $message = "بدأ الموظف {$employee_name} شيفت: {$shift_name}";
                $notification_type = 'shift_completed';
                $priority = 'medium';
                break;

            case 'shift_end':
                $title = 'انتهاء شيفت';
                $message = "انتهى الموظف {$employee_name} من شيفت: {$shift_name}";
                $notification_type = 'shift_summary_ready';
                $priority = 'medium';
                break;

            default:
                return; // نوع غير معروف
        }

        // إدراج الإشعار
        $stmt = $pdo->prepare("
            INSERT INTO admin_notifications
            (client_id, notification_type, title, message, related_id, priority, is_read, is_dismissed, created_at)
            VALUES (?, ?, ?, ?, ?, ?, 0, 0, NOW())
        ");

        $stmt->execute([
            $client_id,
            $notification_type,
            $title,
            $message,
            $shift_id,
            $priority
        ]);

    } catch (Exception $e) {
        // تسجيل الخطأ في ملف اللوج
        error_log("خطأ في إرسال إشعار الشيفت: " . $e->getMessage());
    }
}

// التحقق من تسجيل دخول الموظف
if (!isset($_SESSION['employee_id'])) {
    header('Location: employee-login.php');
    exit;
}

$employee_id = $_SESSION['employee_id'];
$client_id = $_SESSION['client_id'];

$page_title = "التحكم في الشيفت";
$active_page = "shift_control";

// معالجة العمليات
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'start_shift':
                    // التحقق من عدم وجود شيفت نشط
                    $stmt = $pdo->prepare("
                        SELECT sa.attendance_id, s.shift_name
                        FROM shift_attendance sa
                        JOIN shifts s ON sa.shift_id = s.shift_id
                        WHERE sa.employee_id = ? AND sa.check_out_time IS NULL
                    ");
                    $stmt->execute([$employee_id]);
                    $active_shift = $stmt->fetch();

                    if ($active_shift) {
                        $error = "لديك شيفت نشط بالفعل: " . $active_shift['shift_name'];
                        break;
                    }

                    // جلب إعدادات الشيفتات
                    $stmt = $pdo->prepare("SELECT * FROM shift_settings WHERE client_id = ?");
                    $stmt->execute([$client_id]);
                    $settings = $stmt->fetch();

                    $available_shift = null;

                    if (isset($_POST['shift_id']) && $_POST['shift_id'] !== 'manual') {
                        // بدء شيفت مجدول
                        $stmt = $pdo->prepare("
                            SELECT s.shift_id, s.shift_name, s.start_time, s.end_time
                            FROM shifts s
                            WHERE s.shift_id = ? AND s.client_id = ? AND s.status = 'scheduled'
                            AND NOT EXISTS (
                                SELECT 1 FROM shift_attendance sa
                                WHERE sa.shift_id = s.shift_id
                                AND sa.employee_id = ?
                            )
                        ");
                        $stmt->execute([$_POST['shift_id'], $client_id, $employee_id]);
                        $available_shift = $stmt->fetch();

                    } elseif (isset($_POST['shift_id']) && $_POST['shift_id'] === 'manual' && $settings && $settings['allow_manual_shifts']) {
                        // إنشاء شيفت يدوي
                        $shift_duration = $settings['auto_shift_duration'] ?? 480;
                        $start_time = new DateTime();
                        $end_time = clone $start_time;
                        $end_time->add(new DateInterval('PT' . $shift_duration . 'M'));

                        // إنشاء شيفت جديد
                        $stmt = $pdo->prepare("
                            INSERT INTO shifts (client_id, shift_name, shift_date, start_time, end_time,
                                              break_duration, status, shift_type, auto_created, created_by)
                            VALUES (?, ?, CURDATE(), ?, ?, ?, 'active', 'manual', 1, ?)
                        ");
                        $stmt->execute([
                            $client_id,
                            'وردية يدوية - ' . date('H:i'),
                            $start_time->format('H:i:s'),
                            $end_time->format('H:i:s'),
                            $settings['default_break_duration'] ?? 30,
                            $employee_id
                        ]);

                        $shift_id = $pdo->lastInsertId();
                        $available_shift = [
                            'shift_id' => $shift_id,
                            'shift_name' => 'وردية يدوية - ' . date('H:i'),
                            'start_time' => $start_time->format('H:i:s'),
                            'end_time' => $end_time->format('H:i:s')
                        ];

                    } else {
                        // البحث عن شيفت مجدول لليوم الحالي (النظام القديم)
                        $stmt = $pdo->prepare("
                            SELECT s.shift_id, s.shift_name, s.start_time, s.end_time
                            FROM shifts s
                            WHERE s.client_id = ?
                            AND s.shift_date = CURDATE()
                            AND s.status = 'scheduled'
                            AND NOT EXISTS (
                                SELECT 1 FROM shift_attendance sa
                                WHERE sa.shift_id = s.shift_id
                                AND sa.employee_id = ?
                            )
                            ORDER BY s.start_time ASC
                            LIMIT 1
                        ");
                        $stmt->execute([$client_id, $employee_id]);
                        $available_shift = $stmt->fetch();
                    }

                    if (!$available_shift) {
                        if ($settings && $settings['allow_manual_shifts']) {
                            $error = "لا يوجد شيفت متاح لك اليوم. يمكنك بدء وردية يدوية.";
                        } else {
                            $error = "لا يوجد شيفت متاح لك اليوم";
                        }
                        break;
                    }
                    
                    // بدء الشيفت
                    $pdo->beginTransaction();
                    
                    // إضافة سجل حضور
                    $stmt = $pdo->prepare("
                        INSERT INTO shift_attendance (shift_id, employee_id, check_in_time, recorded_by) 
                        VALUES (?, ?, NOW(), ?)
                    ");
                    $stmt->execute([$available_shift['shift_id'], $employee_id, $employee_id]);
                    
                    // تحديث حالة الشيفت إلى نشط
                    $stmt = $pdo->prepare("UPDATE shifts SET status = 'active' WHERE shift_id = ?");
                    $stmt->execute([$available_shift['shift_id']]);
                    
                    // تسجيل نشاط بدء الشيفت
                    $stmt = $pdo->prepare("
                        INSERT INTO shift_activities (shift_id, employee_id, activity_type, activity_description, activity_time)
                        VALUES (?, ?, 'other', 'بدء الشيفت', NOW())
                    ");
                    $stmt->execute([$available_shift['shift_id'], $employee_id]);

                    // إرسال إشعار للإدمن عن بدء الشيفت
                    sendShiftNotification($pdo, $client_id, 'shift_start', $available_shift['shift_id'], $employee_id, $available_shift['shift_name']);

                    $pdo->commit();
                    $message = "تم بدء الشيفت بنجاح: " . $available_shift['shift_name'];
                    break;
                    
                case 'end_shift':
                    // البحث عن الشيفت النشط
                    $stmt = $pdo->prepare("
                        SELECT sa.attendance_id, sa.shift_id, sa.check_in_time, s.shift_name
                        FROM shift_attendance sa 
                        JOIN shifts s ON sa.shift_id = s.shift_id 
                        WHERE sa.employee_id = ? AND sa.check_out_time IS NULL
                    ");
                    $stmt->execute([$employee_id]);
                    $active_shift = $stmt->fetch();
                    
                    if (!$active_shift) {
                        $error = "لا يوجد شيفت نشط لإنهائه";
                        break;
                    }
                    
                    $pdo->beginTransaction();
                    
                    // تسجيل وقت الانصراف
                    $stmt = $pdo->prepare("
                        UPDATE shift_attendance 
                        SET check_out_time = NOW(), notes = ? 
                        WHERE attendance_id = ?
                    ");
                    $stmt->execute([$_POST['end_notes'] ?? '', $active_shift['attendance_id']]);
                    
                    // تسجيل نشاط إنهاء الشيفت
                    $stmt = $pdo->prepare("
                        INSERT INTO shift_activities (shift_id, employee_id, activity_type, activity_description, activity_time) 
                        VALUES (?, ?, 'other', 'إنهاء الشيفت', NOW())
                    ");
                    $stmt->execute([$active_shift['shift_id'], $employee_id]);
                    
                    // إنشاء تقرير الشيفت
                    $report_data = generateShiftReport($pdo, $active_shift['shift_id'], $employee_id);
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO shift_reports (
                            shift_id, employee_id, total_sessions, total_sales, total_revenue, 
                            customers_served, products_sold, average_session_duration, 
                            performance_score, summary_notes
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $active_shift['shift_id'],
                        $employee_id,
                        $report_data['total_sessions'],
                        $report_data['total_sales'],
                        $report_data['total_revenue'],
                        $report_data['customers_served'],
                        $report_data['products_sold'],
                        $report_data['average_session_duration'],
                        $report_data['performance_score'],
                        $report_data['summary_notes']
                    ]);
                    
                    // تحديث حالة الشيفت إلى مكتمل
                    $stmt = $pdo->prepare("UPDATE shifts SET status = 'completed' WHERE shift_id = ?");
                    $stmt->execute([$active_shift['shift_id']]);

                    // إرسال إشعار للإدمن عن إنهاء الشيفت
                    sendShiftNotification($pdo, $client_id, 'shift_end', $active_shift['shift_id'], $employee_id, $active_shift['shift_name']);

                    $pdo->commit();
                    $message = "تم إنهاء الشيفت بنجاح وإنشاء التقرير";
                    break;
            }
        }
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        $error = "حدث خطأ: " . $e->getMessage();
    }
}

// جلب معلومات الشيفت الحالي
$current_shift = null;
$stmt = $pdo->prepare("
    SELECT 
        sa.attendance_id,
        sa.shift_id,
        sa.check_in_time,
        s.shift_name,
        s.start_time,
        s.end_time,
        TIMESTAMPDIFF(MINUTE, sa.check_in_time, NOW()) as minutes_worked
    FROM shift_attendance sa 
    JOIN shifts s ON sa.shift_id = s.shift_id 
    WHERE sa.employee_id = ? AND sa.check_out_time IS NULL
");
$stmt->execute([$employee_id]);
$current_shift = $stmt->fetch();

// جلب إعدادات الشيفتات للعميل
$stmt = $pdo->prepare("
    SELECT * FROM shift_settings WHERE client_id = ?
");
$stmt->execute([$client_id]);
$shift_settings = $stmt->fetch();

// إعدادات افتراضية إذا لم توجد
if (!$shift_settings) {
    $shift_settings = [
        'require_scheduled_shifts' => 1,
        'allow_manual_shifts' => 0,
        'auto_shift_duration' => 480,
        'max_shift_duration' => 720,
        'require_break' => 1,
        'default_break_duration' => 30,
        'shift_overlap_allowed' => 0
    ];
}

// جلب الشيفتات المتاحة لليوم (إذا كانت مطلوبة)
$available_shifts = [];
if (!$current_shift) {
    if ($shift_settings['require_scheduled_shifts']) {
        // البحث عن الشيفتات المجدولة
        $stmt = $pdo->prepare("
            SELECT s.shift_id, s.shift_name, s.start_time, s.end_time, s.break_duration
            FROM shifts s
            WHERE s.client_id = ?
            AND s.shift_date = CURDATE()
            AND s.status = 'scheduled'
            AND NOT EXISTS (
                SELECT 1 FROM shift_attendance sa
                WHERE sa.shift_id = s.shift_id
                AND sa.employee_id = ?
            )
            ORDER BY s.start_time ASC
        ");
        $stmt->execute([$client_id, $employee_id]);
        $available_shifts = $stmt->fetchAll();
    }
}

// دالة إنشاء تقرير الشيفت
function generateShiftReport($pdo, $shift_id, $employee_id) {
    // جلب إحصائيات الجلسات
    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as total_sessions,
            COUNT(DISTINCT customer_id) as customers_served,
            AVG(TIMESTAMPDIFF(MINUTE, start_time, end_time)) as avg_duration,
            SUM(total_cost) as total_revenue
        FROM sessions
        WHERE created_by = ?
        AND DATE(start_time) = (SELECT shift_date FROM shifts WHERE shift_id = ?)
    ");
    $stmt->execute([$employee_id, $shift_id]);
    $session_stats = $stmt->fetch();
    
    // جلب إحصائيات المبيعات
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_orders,
            SUM(total_amount) as total_sales,
            SUM(quantity) as products_sold
        FROM orders o
        JOIN sessions s ON o.session_id = s.session_id
        WHERE s.created_by = ?
        AND DATE(o.created_at) = (SELECT shift_date FROM shifts WHERE shift_id = ?)
    ");
    $stmt->execute([$employee_id, $shift_id]);
    $sales_stats = $stmt->fetch();
    
    // حساب نقاط الأداء (من 10)
    $performance_score = 5.0; // نقاط أساسية
    
    if ($session_stats['total_sessions'] > 5) $performance_score += 1.0;
    if ($session_stats['customers_served'] > 3) $performance_score += 1.0;
    if ($sales_stats['total_sales'] > 100) $performance_score += 1.0;
    if ($session_stats['avg_duration'] > 30) $performance_score += 1.0;
    if ($sales_stats['products_sold'] > 10) $performance_score += 1.0;
    
    $performance_score = min($performance_score, 10.0);
    
    return [
        'total_sessions' => $session_stats['total_sessions'] ?? 0,
        'total_sales' => $sales_stats['total_sales'] ?? 0,
        'total_revenue' => $session_stats['total_revenue'] ?? 0,
        'customers_served' => $session_stats['customers_served'] ?? 0,
        'products_sold' => $sales_stats['products_sold'] ?? 0,
        'average_session_duration' => round($session_stats['avg_duration'] ?? 0, 2),
        'performance_score' => $performance_score,
        'summary_notes' => "تقرير تلقائي - الجلسات: " . ($session_stats['total_sessions'] ?? 0) . 
                          ", المبيعات: " . ($sales_stats['total_sales'] ?? 0) . " ريال"
    ];
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">التحكم في الشيفت</h1>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <?php if ($current_shift): ?>
                        <!-- الشيفت النشط -->
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-clock"></i>
                                    الشيفت النشط
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><strong>اسم الشيفت:</strong> <?php echo htmlspecialchars($current_shift['shift_name']); ?></h6>
                                        <p><strong>وقت البدء:</strong> <?php echo date('H:i', strtotime($current_shift['start_time'])); ?></p>
                                        <p><strong>وقت الانتهاء المتوقع:</strong> <?php echo date('H:i', strtotime($current_shift['end_time'])); ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>بدأت في:</strong> <?php echo date('H:i', strtotime($current_shift['check_in_time'])); ?></p>
                                        <p><strong>مدة العمل:</strong> 
                                            <span class="badge bg-primary">
                                                <?php 
                                                $hours = floor($current_shift['minutes_worked'] / 60);
                                                $minutes = $current_shift['minutes_worked'] % 60;
                                                echo $hours . 'س ' . $minutes . 'د';
                                                ?>
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <!-- نموذج إنهاء الشيفت -->
                                <form method="POST" onsubmit="return confirm('هل أنت متأكد من إنهاء الشيفت؟')">
                                    <input type="hidden" name="action" value="end_shift">
                                    <div class="mb-3">
                                        <label for="end_notes" class="form-label">ملاحظات الإنهاء (اختيارية)</label>
                                        <textarea class="form-control" id="end_notes" name="end_notes" rows="3" 
                                                  placeholder="أي ملاحظات أو مشاكل حدثت أثناء الشيفت..."></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-danger btn-lg">
                                        <i class="fas fa-stop-circle"></i>
                                        إنهاء الشيفت
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- بدء شيفت جديد -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-play-circle"></i>
                                    بدء شيفت جديد
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($available_shifts)): ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        لا توجد شيفتات متاحة لك اليوم. يرجى التواصل مع المدير.
                                    </div>
                                <?php else: ?>
                                    <?php if ($shift_settings['require_scheduled_shifts'] && !empty($available_shifts)): ?>
                                        <p class="text-muted">الشيفتات المجدولة المتاحة لك اليوم:</p>

                                        <?php foreach ($available_shifts as $shift): ?>
                                            <div class="card mb-3 border-primary">
                                                <div class="card-body">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-8">
                                                            <h6 class="card-title"><?php echo htmlspecialchars($shift['shift_name']); ?></h6>
                                                            <p class="card-text">
                                                                <i class="fas fa-clock"></i>
                                                                <?php echo date('H:i', strtotime($shift['start_time'])); ?> -
                                                                <?php echo date('H:i', strtotime($shift['end_time'])); ?>
                                                                <span class="badge bg-secondary ms-2">
                                                                    استراحة: <?php echo $shift['break_duration']; ?> دقيقة
                                                                </span>
                                                            </p>
                                                        </div>
                                                        <div class="col-md-4 text-end">
                                                            <form method="POST" style="display: inline;">
                                                                <input type="hidden" name="action" value="start_shift">
                                                                <input type="hidden" name="shift_id" value="<?php echo $shift['shift_id']; ?>">
                                                                <button type="submit" class="btn btn-success btn-lg">
                                                                    <i class="fas fa-play"></i>
                                                                    بدء الشيفت
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>

                                    <?php if ($shift_settings['allow_manual_shifts']): ?>
                                        <div class="card mb-3 border-success">
                                            <div class="card-body">
                                                <div class="row align-items-center">
                                                    <div class="col-md-8">
                                                        <h6 class="card-title">
                                                            <i class="fas fa-plus-circle text-success"></i>
                                                            وردية يدوية
                                                        </h6>
                                                        <p class="card-text text-muted">
                                                            <i class="fas fa-info-circle"></i>
                                                            بدء وردية فورية بمدة <?php echo floor($shift_settings['auto_shift_duration'] / 60); ?> ساعات
                                                            مع استراحة <?php echo $shift_settings['default_break_duration']; ?> دقيقة
                                                        </p>
                                                    </div>
                                                    <div class="col-md-4 text-end">
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="start_shift">
                                                            <input type="hidden" name="shift_id" value="manual">
                                                            <button type="submit" class="btn btn-success btn-lg">
                                                                <i class="fas fa-play"></i>
                                                                بدء وردية يدوية
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (empty($available_shifts) && !$shift_settings['allow_manual_shifts']): ?>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i>
                                            لا توجد شيفتات متاحة لك اليوم. يرجى التواصل مع الإدارة.
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-4">
                    <!-- إحصائيات سريعة -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">إحصائيات اليوم</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            // جلب إحصائيات اليوم
                            $stmt = $pdo->prepare("
                                SELECT 
                                    COUNT(*) as sessions_today,
                                    SUM(total_cost) as revenue_today
                                FROM sessions 
                                WHERE created_by = ? AND DATE(start_time) = CURDATE()
                            ");
                            $stmt->execute([$employee_id]);
                            $today_stats = $stmt->fetch();
                            
                            $stmt = $pdo->prepare("
                                SELECT COUNT(*) as orders_today, SUM(total_amount) as sales_today
                                FROM orders o
                                JOIN sessions s ON o.session_id = s.session_id
                                WHERE s.created_by = ? AND DATE(o.created_at) = CURDATE()
                            ");
                            $stmt->execute([$employee_id]);
                            $sales_today = $stmt->fetch();
                            ?>
                            
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary"><?php echo $today_stats['sessions_today'] ?? 0; ?></h4>
                                        <small class="text-muted">جلسات</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success"><?php echo number_format($today_stats['revenue_today'] ?? 0, 2); ?></h4>
                                    <small class="text-muted">ريال إيرادات</small>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h5 class="text-info"><?php echo $sales_today['orders_today'] ?? 0; ?></h5>
                                        <small class="text-muted">طلبات</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h5 class="text-warning"><?php echo number_format($sales_today['sales_today'] ?? 0, 2); ?></h5>
                                    <small class="text-muted">ريال مبيعات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- آخر الأنشطة -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">آخر الأنشطة</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $stmt = $pdo->prepare("
                                SELECT activity_description, activity_time, activity_type
                                FROM shift_activities 
                                WHERE employee_id = ? 
                                ORDER BY activity_time DESC 
                                LIMIT 5
                            ");
                            $stmt->execute([$employee_id]);
                            $recent_activities = $stmt->fetchAll();
                            ?>
                            
                            <?php if (empty($recent_activities)): ?>
                                <p class="text-muted">لا توجد أنشطة حديثة</p>
                            <?php else: ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach ($recent_activities as $activity): ?>
                                        <div class="list-group-item px-0 py-2">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <small class="text-muted"><?php echo htmlspecialchars($activity['activity_description']); ?></small>
                                                </div>
                                                <small class="text-muted"><?php echo date('H:i', strtotime($activity['activity_time'])); ?></small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
// تحديث الوقت كل دقيقة
setInterval(function() {
    location.reload();
}, 60000);
</script>
