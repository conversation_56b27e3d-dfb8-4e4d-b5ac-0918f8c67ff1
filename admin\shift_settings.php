<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once '../config/constants.php';
require_once 'includes/auth.php';
require_once 'includes/admin-permissions.php';

// جلب الصفحات المسموحة للإدمن الحالي
$allowed_pages = getAllowedAdminPages();
$allowed_page_names = array_column($allowed_pages, 'page_name');

// دالة للتحقق من إمكانية عرض رابط في القائمة
function canShowMenuItem($page_name) {
    global $allowed_page_names;
    return in_array($page_name, $allowed_page_names) || hasAdminPagePermission($page_name);
}

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $pdo->beginTransaction();
        
        if ($_POST['action'] === 'update_settings') {
            $client_id = (int)$_POST['client_id'];
            $require_scheduled = isset($_POST['require_scheduled_shifts']) ? 1 : 0;
            $allow_manual = isset($_POST['allow_manual_shifts']) ? 1 : 0;
            $auto_duration = (int)$_POST['auto_shift_duration'];
            $max_duration = (int)$_POST['max_shift_duration'];
            $require_break = isset($_POST['require_break']) ? 1 : 0;
            $break_duration = (int)$_POST['default_break_duration'];
            $overlap_allowed = isset($_POST['shift_overlap_allowed']) ? 1 : 0;
            
            // تحديث أو إدراج الإعدادات
            $stmt = $pdo->prepare("
                INSERT INTO shift_settings 
                (client_id, require_scheduled_shifts, allow_manual_shifts, auto_shift_duration, 
                 max_shift_duration, require_break, default_break_duration, shift_overlap_allowed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                require_scheduled_shifts = VALUES(require_scheduled_shifts),
                allow_manual_shifts = VALUES(allow_manual_shifts),
                auto_shift_duration = VALUES(auto_shift_duration),
                max_shift_duration = VALUES(max_shift_duration),
                require_break = VALUES(require_break),
                default_break_duration = VALUES(default_break_duration),
                shift_overlap_allowed = VALUES(shift_overlap_allowed),
                updated_at = CURRENT_TIMESTAMP
            ");
            
            $stmt->execute([
                $client_id, $require_scheduled, $allow_manual, $auto_duration,
                $max_duration, $require_break, $break_duration, $overlap_allowed
            ]);
            
            $pdo->commit();
            $_SESSION['success'] = "تم تحديث إعدادات الورديات بنجاح";
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $_SESSION['error'] = "حدث خطأ: " . $e->getMessage();
    }
    
    header('Location: shift_settings.php');
    exit;
}

// جلب جميع العملاء
$clients = $pdo->query("
    SELECT c.client_id, c.business_name, c.owner_name,
           ss.require_scheduled_shifts, ss.allow_manual_shifts,
           ss.auto_shift_duration, ss.max_shift_duration,
           ss.require_break, ss.default_break_duration, ss.shift_overlap_allowed
    FROM clients c
    LEFT JOIN shift_settings ss ON c.client_id = ss.client_id
    ORDER BY c.business_name
")->fetchAll();

$page_title = "إعدادات الشيفتات";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-cogs me-2"></i>إعدادات الورديات</h1>
            </div>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-building me-2"></i>إعدادات الورديات لكل عميل
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>اسم المحل</th>
                                            <th>صاحب المحل</th>
                                            <th>نظام الورديات</th>
                                            <th>الإعدادات</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($clients as $client): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($client['business_name']); ?></strong>
                                                </td>
                                                <td><?php echo htmlspecialchars($client['owner_name']); ?></td>
                                                <td>
                                                    <?php if ($client['require_scheduled_shifts']): ?>
                                                        <span class="badge bg-primary">ورديات مجدولة</span>
                                                    <?php endif; ?>
                                                    <?php if ($client['allow_manual_shifts']): ?>
                                                        <span class="badge bg-success">ورديات يدوية</span>
                                                    <?php endif; ?>
                                                    <?php if (!$client['require_scheduled_shifts'] && !$client['allow_manual_shifts']): ?>
                                                        <span class="badge bg-secondary">غير محدد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        مدة الوردية: <?php echo $client['auto_shift_duration'] ?? 480; ?> دقيقة<br>
                                                        الاستراحة: <?php echo $client['require_break'] ? 'مطلوبة' : 'اختيارية'; ?>
                                                        (<?php echo $client['default_break_duration'] ?? 30; ?> دقيقة)
                                                    </small>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#settingsModal<?php echo $client['client_id']; ?>">
                                                        <i class="fas fa-edit"></i> تعديل
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نوافذ تعديل الإعدادات -->
<?php foreach ($clients as $client): ?>
<div class="modal fade" id="settingsModal<?php echo $client['client_id']; ?>" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">إعدادات الورديات - <?php echo htmlspecialchars($client['business_name']); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_settings">
                    <input type="hidden" name="client_id" value="<?php echo $client['client_id']; ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">نوع نظام الورديات</h6>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="require_scheduled_shifts" 
                                       id="scheduled<?php echo $client['client_id']; ?>"
                                       <?php echo $client['require_scheduled_shifts'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="scheduled<?php echo $client['client_id']; ?>">
                                    <strong>يتطلب ورديات مجدولة مسبقاً</strong>
                                    <small class="d-block text-muted">يجب إنشاء جدول ورديات قبل بدء العمل</small>
                                </label>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="allow_manual_shifts" 
                                       id="manual<?php echo $client['client_id']; ?>"
                                       <?php echo $client['allow_manual_shifts'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="manual<?php echo $client['client_id']; ?>">
                                    <strong>السماح بالورديات اليدوية</strong>
                                    <small class="d-block text-muted">يمكن للموظفين بدء الوردية مباشرة</small>
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">إعدادات الوقت</h6>
                            
                            <div class="mb-3">
                                <label class="form-label">مدة الوردية الافتراضية (دقيقة)</label>
                                <input type="number" class="form-control" name="auto_shift_duration" 
                                       value="<?php echo $client['auto_shift_duration'] ?? 480; ?>" min="60" max="1440">
                                <small class="text-muted">480 دقيقة = 8 ساعات</small>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">أقصى مدة للوردية (دقيقة)</label>
                                <input type="number" class="form-control" name="max_shift_duration" 
                                       value="<?php echo $client['max_shift_duration'] ?? 720; ?>" min="120" max="1440">
                                <small class="text-muted">720 دقيقة = 12 ساعة</small>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">إعدادات الاستراحة</h6>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="require_break" 
                                       id="break<?php echo $client['client_id']; ?>"
                                       <?php echo $client['require_break'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="break<?php echo $client['client_id']; ?>">
                                    <strong>الاستراحة مطلوبة</strong>
                                </label>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">مدة الاستراحة الافتراضية (دقيقة)</label>
                                <input type="number" class="form-control" name="default_break_duration" 
                                       value="<?php echo $client['default_break_duration'] ?? 30; ?>" min="15" max="120">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">إعدادات متقدمة</h6>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="shift_overlap_allowed" 
                                       id="overlap<?php echo $client['client_id']; ?>"
                                       <?php echo $client['shift_overlap_allowed'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="overlap<?php echo $client['client_id']; ?>">
                                    <strong>السماح بتداخل الورديات</strong>
                                    <small class="d-block text-muted">يمكن للموظف بدء وردية جديدة قبل انتهاء السابقة</small>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endforeach; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة النماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const scheduledCheck = form.querySelector('input[name="require_scheduled_shifts"]');
            const manualCheck = form.querySelector('input[name="allow_manual_shifts"]');
            
            if (!scheduledCheck.checked && !manualCheck.checked) {
                e.preventDefault();
                alert('يجب اختيار نوع واحد على الأقل من أنواع الورديات');
                return false;
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
