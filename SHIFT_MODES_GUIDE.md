# دليل أنظمة الشيفتات المرنة - PlayGood

## نظرة عامة
تم إضافة نظام مرن لإدارة الشيفتات يتيح للإدارة اختيار نوع نظام الشيفتات المناسب لكل محل:

### أنواع أنظمة الشيفتات:

#### 1. **النظام المجدول (Scheduled Shifts)**
- يتطلب إنشاء جدول ورديات مسبقاً
- الموظفون يمكنهم فقط بدء الشيفتات المجدولة لهم
- مناسب للمحلات الكبيرة مع جداول عمل ثابتة

#### 2. **النظام اليدوي (Manual Shifts)**  
- الموظفون يمكنهم بدء الشيفت مباشرة بدون جدولة مسبقة
- يتم إنشاء الشيفت تلقائياً عند البدء
- مناسب للمحلات الصغيرة أو العمل المرن

#### 3. **النظام المختلط**
- يدعم كلا النوعين معاً
- الموظفون يمكنهم بدء شيفت مجدول أو يدوي

## المكونات الجديدة

### 1. جدول إعدادات الشيفتات (`shift_settings`)
```sql
- client_id: معرف العميل
- require_scheduled_shifts: هل يتطلب ورديات مجدولة (افتراضي: نعم)
- allow_manual_shifts: السماح بالورديات اليدوية (افتراضي: لا)
- auto_shift_duration: مدة الوردية التلقائية (افتراضي: 480 دقيقة = 8 ساعات)
- max_shift_duration: أقصى مدة للوردية (افتراضي: 720 دقيقة = 12 ساعة)
- require_break: هل تتطلب استراحة (افتراضي: نعم)
- default_break_duration: مدة الاستراحة الافتراضية (افتراضي: 30 دقيقة)
- shift_overlap_allowed: السماح بتداخل الورديات (افتراضي: لا)
```

### 2. تحديثات جدول الشيفتات (`shifts`)
```sql
- shift_type: نوع الوردية (scheduled/manual/auto)
- auto_created: هل تم إنشاؤها تلقائياً (0/1)
```

### 3. صفحة إعدادات الشيفتات (`admin/shift_settings.php`)
- واجهة إدارية لتكوين إعدادات كل محل
- إمكانية تفعيل/إلغاء تفعيل كل نوع من أنواع الشيفتات
- تحديد المدد الزمنية والإعدادات المتقدمة

### 4. تحديث واجهة الموظف (`client/shift_control.php`)
- عرض الشيفتات المجدولة (إن وجدت)
- خيار بدء وردية يدوية (إذا كان مفعلاً)
- رسائل توضيحية حسب النظام المفعل

## طريقة الاستخدام

### للإدارة:

#### 1. تكوين إعدادات الشيفتات
1. الذهاب إلى "إعدادات الشيفتات" من القائمة الجانبية
2. اختيار المحل المراد تكوينه
3. الضغط على "تعديل"
4. تحديد نوع النظام:
   - ✅ **ورديات مجدولة**: يتطلب إنشاء جدول ورديات مسبقاً
   - ✅ **ورديات يدوية**: يمكن للموظفين بدء الوردية مباشرة
5. تحديد الإعدادات الزمنية:
   - مدة الوردية الافتراضية
   - أقصى مدة للوردية
   - مدة الاستراحة
6. حفظ الإعدادات

#### 2. إنشاء الشيفتات المجدولة (اختياري)
- إذا كان النظام المجدول مفعلاً، يجب إنشاء الشيفتات من صفحة "الورديات"
- إذا كان النظام اليدوي مفعلاً، لا حاجة لإنشاء شيفتات مسبقة

### للموظفين:

#### النظام المجدول:
1. تسجيل الدخول كموظف
2. الذهاب إلى "التحكم في الشيفت"
3. اختيار الشيفت المجدول من القائمة
4. الضغط على "بدء الشيفت"

#### النظام اليدوي:
1. تسجيل الدخول كموظف
2. الذهاب إلى "التحكم في الشيفت"
3. الضغط على "بدء وردية يدوية"
4. سيتم إنشاء الشيفت تلقائياً وبدء العمل

#### النظام المختلط:
- يمكن اختيار بدء شيفت مجدول أو وردية يدوية حسب الحاجة

## المميزات الجديدة

### 1. **المرونة الكاملة**
- كل محل يمكن أن يكون له نظام شيفتات مختلف
- إمكانية التبديل بين الأنظمة في أي وقت

### 2. **الشيفتات اليدوية**
- إنشاء تلقائي للشيفت عند البدء
- مدة محددة مسبقاً قابلة للتخصيص
- تسجيل تلقائي لجميع الأنشطة

### 3. **إعدادات متقدمة**
- تحديد مدة الاستراحة
- أقصى مدة للوردية
- السماح بتداخل الورديات

### 4. **التوافق مع النظام الحالي**
- جميع الميزات الحالية تعمل بنفس الطريقة
- تقارير الشيفتات تدعم جميع الأنواع
- تتبع الأنشطة يعمل مع جميع الأنظمة

## أمثلة الاستخدام

### محل صغير (نظام يدوي):
```
الإعدادات:
- ورديات مجدولة: ❌
- ورديات يدوية: ✅
- مدة الوردية: 8 ساعات
- استراحة: 30 دقيقة

الاستخدام:
- الموظف يأتي ويضغط "بدء وردية يدوية"
- يعمل لمدة 8 ساعات
- يضغط "إنهاء الشيفت"
```

### محل كبير (نظام مجدول):
```
الإعدادات:
- ورديات مجدولة: ✅
- ورديات يدوية: ❌
- جدول ورديات ثابت

الاستخدام:
- الإدارة تنشئ جدول ورديات أسبوعي
- الموظف يختار شيفته المجدول
- يبدأ العمل في الوقت المحدد
```

### محل متوسط (نظام مختلط):
```
الإعدادات:
- ورديات مجدولة: ✅
- ورديات يدوية: ✅

الاستخدام:
- ورديات أساسية مجدولة
- ورديات إضافية يدوية عند الحاجة
- مرونة كاملة للموظفين
```

## الفوائد

### للإدارة:
- **مرونة في التشغيل**: اختيار النظام المناسب لكل محل
- **تقليل الأعباء الإدارية**: لا حاجة لجدولة في النظام اليدوي
- **تحكم كامل**: إعدادات مفصلة لكل محل

### للموظفين:
- **سهولة الاستخدام**: بدء العمل بنقرة واحدة
- **مرونة في الأوقات**: خاصة في النظام اليدوي
- **وضوح في التعليمات**: واجهة واضحة لكل نظام

### للنظام:
- **تتبع شامل**: جميع الأنشطة مسجلة بغض النظر عن نوع الشيفت
- **تقارير موحدة**: تقارير شاملة لجميع أنواع الشيفتات
- **قابلية التوسع**: سهولة إضافة أنواع جديدة من الشيفتات

## الملفات المضافة/المحدثة

### ملفات جديدة:
- `add_shift_settings.sql` - إنشاء جدول الإعدادات
- `admin/shift_settings.php` - واجهة إعدادات الشيفتات
- `setup_shift_settings.php` - تنفيذ إعدادات قاعدة البيانات

### ملفات محدثة:
- `client/shift_control.php` - دعم الأنظمة المختلفة
- `admin/includes/sidebar.php` - إضافة رابط الإعدادات

---

**تاريخ الإنشاء:** 27 سبتمبر 2025  
**الحالة:** جاهز للاستخدام  
**المطور:** Augment Agent
