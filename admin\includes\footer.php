        </div>
    </div>
</div>

<!-- <PERSON>trap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Font Awesome -->
<script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>

<script>
// تفعيل tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});

// تفعيل popovers
var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
    return new bootstrap.Popover(popoverTriggerEl);
});

// نظام الإشعارات
class AdminNotifications {
    constructor() {
        this.loadNotifications();
        this.initEventListeners();
        // تحديث الإشعارات كل 30 ثانية
        setInterval(() => this.loadNotifications(), 30000);
    }

    async loadNotifications() {
        try {
            const response = await fetch('api/get_notifications.php?limit=10&unread_only=false');
            const data = await response.json();

            if (data.success) {
                this.updateNotificationBadge(data.unread_count);
                this.renderNotifications(data.notifications);
            }
        } catch (error) {
            console.error('خطأ في تحميل الإشعارات:', error);
        }
    }

    updateNotificationBadge(count) {
        const badge = document.getElementById('notificationBadge');
        if (badge && count > 0) {
            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = 'block';
        } else if (badge) {
            badge.style.display = 'none';
        }
    }

    renderNotifications(notifications) {
        const container = document.getElementById('notificationsList');
        if (!container) return;

        if (notifications.length === 0) {
            container.innerHTML = '<div class="text-center p-3 text-muted">لا توجد إشعارات</div>';
            return;
        }

        let html = '';
        notifications.forEach(notification => {
            const isUnread = !notification.is_read;
            html += `
                <div class="dropdown-item ${isUnread ? 'bg-light' : ''}" data-notification-id="${notification.notification_id}">
                    <div class="d-flex align-items-start">
                        <div class="me-2">
                            <i class="${notification.icon} text-${notification.color}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold">${notification.title}</div>
                            <div class="small text-muted">${notification.message}</div>
                            <div class="small text-muted">
                                <i class="fas fa-store me-1"></i>${notification.business_name || 'غير محدد'}
                                <span class="ms-2">${notification.formatted_date}</span>
                            </div>
                        </div>
                        ${isUnread ? '<div class="ms-2"><span class="badge bg-primary">جديد</span></div>' : ''}
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    initEventListeners() {
        // تحديد الكل كمقروء
        const markAllBtn = document.getElementById('markAllRead');
        if (markAllBtn) {
            markAllBtn.addEventListener('click', async () => {
                try {
                    const response = await fetch('api/mark_notification_read.php', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({mark_all_read: true})
                    });

                    if (response.ok) {
                        this.loadNotifications();
                    }
                } catch (error) {
                    console.error('خطأ في تحديث الإشعارات:', error);
                }
            });
        }

        // تحديد إشعار واحد كمقروء عند النقر
        const notificationsList = document.getElementById('notificationsList');
        if (notificationsList) {
            notificationsList.addEventListener('click', async (e) => {
                const notificationItem = e.target.closest('[data-notification-id]');
                if (notificationItem) {
                    const notificationId = notificationItem.dataset.notificationId;

                    try {
                        await fetch('api/mark_notification_read.php', {
                            method: 'POST',
                            headers: {'Content-Type': 'application/json'},
                            body: JSON.stringify({notification_id: parseInt(notificationId)})
                        });

                        // إزالة تمييز الإشعار
                        notificationItem.classList.remove('bg-light');
                        const badge = notificationItem.querySelector('.badge');
                        if (badge) badge.remove();

                        // تحديث العداد
                        this.loadNotifications();
                    } catch (error) {
                        console.error('خطأ في تحديث الإشعار:', error);
                    }
                }
            });
        }
    }
}

// تشغيل نظام الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new AdminNotifications();
});
</script>

</body>
</html>
