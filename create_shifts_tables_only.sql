-- إن<PERSON>اء جداول نظام الشيفتات فقط
-- تاريخ الإنشاء: 27 سبتمبر 2025

-- جدول الشيفتات الأساسي
CREATE TABLE IF NOT EXISTS `shifts` (
    `shift_id` int(11) NOT NULL AUTO_INCREMENT,
    `client_id` int(11) NOT NULL,
    `employee_id` int(11) NOT NULL,
    `shift_name` varchar(100) NOT NULL,
    `shift_date` date NOT NULL,
    `scheduled_start` time NOT NULL,
    `scheduled_end` time NOT NULL,
    `actual_start` datetime NULL,
    `actual_end` datetime NULL,
    `status` enum('scheduled', 'active', 'completed', 'cancelled') DEFAULT 'scheduled',
    `break_duration` int(11) DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
    `notes` text NULL,
    `created_by` int(11) NOT NULL COMMENT 'معرف من أنشأ الشيفت',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`shift_id`),
    KEY `idx_client_employee` (`client_id`, `employee_id`),
    KEY `idx_date_status` (`shift_date`, `status`),
    KEY `idx_employee_date` (`employee_id`, `shift_date`),
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`client_id`) ON DELETE CASCADE,
    FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول حضور الشيفتات
CREATE TABLE IF NOT EXISTS `shift_attendance` (
    `attendance_id` int(11) NOT NULL AUTO_INCREMENT,
    `shift_id` int(11) NOT NULL,
    `employee_id` int(11) NOT NULL,
    `check_in_time` datetime NULL,
    `check_out_time` datetime NULL,
    `break_start_time` datetime NULL,
    `break_end_time` datetime NULL,
    `total_hours` decimal(4,2) DEFAULT 0.00,
    `break_hours` decimal(4,2) DEFAULT 0.00,
    `overtime_hours` decimal(4,2) DEFAULT 0.00,
    `status` enum('not_started', 'checked_in', 'on_break', 'checked_out') DEFAULT 'not_started',
    `location_check_in` varchar(255) NULL COMMENT 'موقع تسجيل الدخول',
    `location_check_out` varchar(255) NULL COMMENT 'موقع تسجيل الخروج',
    `ip_address` varchar(45) NULL,
    `notes` text NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`attendance_id`),
    UNIQUE KEY `unique_shift_employee` (`shift_id`, `employee_id`),
    KEY `idx_employee_date` (`employee_id`, `check_in_time`),
    KEY `idx_status` (`status`),
    FOREIGN KEY (`shift_id`) REFERENCES `shifts`(`shift_id`) ON DELETE CASCADE,
    FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول أنشطة الشيفت
CREATE TABLE IF NOT EXISTS `shift_activities` (
    `activity_id` int(11) NOT NULL AUTO_INCREMENT,
    `shift_id` int(11) NOT NULL,
    `employee_id` int(11) NOT NULL,
    `activity_type` enum('session_start', 'session_end', 'sale', 'customer_service', 'maintenance', 'cleaning', 'break', 'other') NOT NULL,
    `activity_description` text NOT NULL,
    `related_id` int(11) NULL COMMENT 'معرف العنصر المرتبط (جلسة، منتج، إلخ)',
    `related_table` varchar(50) NULL COMMENT 'اسم الجدول المرتبط',
    `amount` decimal(10,2) DEFAULT 0.00 COMMENT 'المبلغ المرتبط بالنشاط',
    `activity_time` datetime NOT NULL DEFAULT current_timestamp(),
    `notes` text NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`activity_id`),
    KEY `idx_shift_employee` (`shift_id`, `employee_id`),
    KEY `idx_activity_type` (`activity_type`),
    KEY `idx_activity_time` (`activity_time`),
    KEY `idx_related` (`related_table`, `related_id`),
    FOREIGN KEY (`shift_id`) REFERENCES `shifts`(`shift_id`) ON DELETE CASCADE,
    FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول تقارير الشيفتات
CREATE TABLE IF NOT EXISTS `shift_reports` (
    `report_id` int(11) NOT NULL AUTO_INCREMENT,
    `shift_id` int(11) NOT NULL,
    `employee_id` int(11) NOT NULL,
    `total_sessions` int(11) DEFAULT 0,
    `total_sales` decimal(10,2) DEFAULT 0.00,
    `total_revenue` decimal(10,2) DEFAULT 0.00,
    `customers_served` int(11) DEFAULT 0,
    `products_sold` int(11) DEFAULT 0,
    `average_session_duration` decimal(5,2) DEFAULT 0.00 COMMENT 'متوسط مدة الجلسة بالساعات',
    `performance_score` decimal(3,2) DEFAULT 0.00 COMMENT 'نقاط الأداء من 10',
    `issues_reported` int(11) DEFAULT 0,
    `customer_complaints` int(11) DEFAULT 0,
    `summary_notes` text NULL,
    `generated_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `generated_by` int(11) NULL COMMENT 'معرف من أنشأ التقرير',
    PRIMARY KEY (`report_id`),
    UNIQUE KEY `unique_shift_report` (`shift_id`, `employee_id`),
    KEY `idx_employee_date` (`employee_id`, `generated_at`),
    KEY `idx_performance` (`performance_score`),
    FOREIGN KEY (`shift_id`) REFERENCES `shifts`(`shift_id`) ON DELETE CASCADE,
    FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول إعدادات الشيفتات
CREATE TABLE IF NOT EXISTS `shift_settings` (
    `setting_id` int(11) NOT NULL AUTO_INCREMENT,
    `client_id` int(11) NOT NULL,
    `auto_break_duration` int(11) DEFAULT 30 COMMENT 'مدة الاستراحة التلقائية بالدقائق',
    `max_shift_hours` int(11) DEFAULT 8 COMMENT 'أقصى عدد ساعات للشيفت',
    `overtime_rate` decimal(3,2) DEFAULT 1.50 COMMENT 'معدل الساعات الإضافية',
    `require_location` tinyint(1) DEFAULT 0 COMMENT 'يتطلب تسجيل الموقع',
    `auto_generate_reports` tinyint(1) DEFAULT 1 COMMENT 'إنشاء التقارير تلقائياً',
    `notification_enabled` tinyint(1) DEFAULT 1 COMMENT 'تفعيل الإشعارات',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`setting_id`),
    UNIQUE KEY `unique_client_settings` (`client_id`),
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- إدراج إعدادات افتراضية لجميع العملاء الموجودين
INSERT IGNORE INTO `shift_settings` (`client_id`)
SELECT `client_id` FROM `clients` WHERE `is_active` = 1;

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX IF NOT EXISTS `idx_shifts_active` ON `shifts` (`status`, `shift_date`);
CREATE INDEX IF NOT EXISTS `idx_attendance_active` ON `shift_attendance` (`status`, `check_in_time`);
CREATE INDEX IF NOT EXISTS `idx_activities_recent` ON `shift_activities` (`activity_time` DESC);

-- إنشاء view مفيد
CREATE OR REPLACE VIEW `active_shifts_view` AS
SELECT 
    s.shift_id,
    s.client_id,
    s.employee_id,
    e.name as employee_name,
    e.role as employee_role,
    c.business_name,
    s.shift_name,
    s.shift_date,
    s.scheduled_start,
    s.scheduled_end,
    s.actual_start,
    s.status,
    sa.check_in_time,
    sa.status as attendance_status,
    TIMESTAMPDIFF(MINUTE, sa.check_in_time, NOW()) as minutes_worked
FROM shifts s
JOIN employees e ON s.employee_id = e.id
JOIN clients c ON s.client_id = c.client_id
LEFT JOIN shift_attendance sa ON s.shift_id = sa.shift_id
WHERE s.status IN ('active', 'scheduled')
ORDER BY s.shift_date DESC, s.scheduled_start ASC;
