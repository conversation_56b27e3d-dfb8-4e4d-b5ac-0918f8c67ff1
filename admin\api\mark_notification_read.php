<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/auth.php';

header('Content-Type: application/json');

// التحقق من تسجيل دخول الإدمن
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح بالوصول']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مسموحة']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

try {
    if (isset($input['notification_id'])) {
        // تحديد إشعار واحد كمقروء
        $stmt = $pdo->prepare("
            UPDATE admin_notifications 
            SET is_read = 1, read_at = NOW() 
            WHERE notification_id = ?
        ");
        $stmt->execute([$input['notification_id']]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديد الإشعار كمقروء'
        ]);
        
    } elseif (isset($input['mark_all_read']) && $input['mark_all_read'] === true) {
        // تحديد جميع الإشعارات كمقروءة
        $client_id = isset($input['client_id']) ? intval($input['client_id']) : null;
        
        if ($client_id) {
            $stmt = $pdo->prepare("
                UPDATE admin_notifications 
                SET is_read = 1, read_at = NOW() 
                WHERE client_id = ? AND is_read = 0
            ");
            $stmt->execute([$client_id]);
        } else {
            $stmt = $pdo->prepare("
                UPDATE admin_notifications 
                SET is_read = 1, read_at = NOW() 
                WHERE is_read = 0
            ");
            $stmt->execute();
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديد جميع الإشعارات كمقروءة'
        ]);
        
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'معاملات غير صحيحة']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في تحديث الإشعار: ' . $e->getMessage()
    ]);
}
?>
