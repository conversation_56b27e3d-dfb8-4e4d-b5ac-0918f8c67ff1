# PlayGood - نظام إدارة محطات الألعاب

## نظرة عامة
PlayGood هو نظام شامل لإدارة محطات الألعاب (Gaming Stations) يوفر إدارة العملاء، الجلسات، المالية، والموظفين.

## الميزات الرئيسية

### إدارة العملاء
- إضافة وتعديل بيانات العملاء
- تتبع تاريخ الجلسات
- إدارة الحسابات والمدفوعات

### إدارة الجلسات
- بدء وإنهاء الجلسات
- تتبع الوقت والتكلفة
- دعم أنواع مختلفة من الألعاب (PS4, PS5, Xbox, PC)

### النظام المالي
- تتبع الإيرادات والمصروفات
- تقارير مالية مفصلة
- إدارة الفواتير

### إدارة الموظفين
- نظام صلاحيات متقدم
- تتبع الحضور والانصراف
- إدارة الورديات

### الكافتيريا والمخزون
- إدارة المنتجات والمشروبات
- تتبع المخزون
- نظام الطلبات

## متطلبات النظام
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- Bootstrap 5
- jQuery

## التثبيت

1. انسخ الملفات إلى مجلد الخادم
2. قم بإنشاء قاعدة بيانات MySQL
3. استورد ملف `station.sql`
4. عدّل إعدادات قاعدة البيانات في `config/database.php`

## الاستخدام

### لوحة الإدارة
- الرابط: `/admin/`
- تسمح بإدارة العملاء والإعدادات العامة

### لوحة العميل
- الرابط: `/client/`
- واجهة العمل اليومي لإدارة الجلسات

## الأمان
- نظام مصادقة متقدم
- حماية من CSRF
- تشفير كلمات المرور
- نظام صلاحيات متدرج

## الدعم الفني
للحصول على الدعم الفني، يرجى مراجعة ملف `دليل_الاستخدام.md`

## الترخيص
جميع الحقوق محفوظة

---
تم تطوير النظام بواسطة فريق PlayGood
