<?php
require_once 'config/database.php';

try {
    echo "فحص بنية جدول shifts:\n";
    $stmt = $pdo->query('DESCRIBE shifts');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo $column['Field'] . ' - ' . $column['Type'] . "\n";
    }
    
    echo "\n\nفحص بنية جدول shift_attendance:\n";
    $stmt = $pdo->query('DESCRIBE shift_attendance');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo $column['Field'] . ' - ' . $column['Type'] . "\n";
    }
    
} catch (Exception $e) {
    echo 'خطأ: ' . $e->getMessage() . "\n";
}
?>
