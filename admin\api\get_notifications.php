<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/auth.php';

header('Content-Type: application/json');

// التحقق من تسجيل دخول الإدمن
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح بالوصول']);
    exit;
}

try {
    // معاملات الاستعلام
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
    $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;
    $unread_only = isset($_GET['unread_only']) && $_GET['unread_only'] === 'true';
    $client_id = isset($_GET['client_id']) ? intval($_GET['client_id']) : null;
    
    // بناء الاستعلام
    $where_conditions = [];
    $params = [];
    
    if ($unread_only) {
        $where_conditions[] = "is_read = 0 AND is_dismissed = 0";
    }
    
    if ($client_id) {
        $where_conditions[] = "client_id = ?";
        $params[] = $client_id;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // جلب الإشعارات
    $query = "
        SELECT 
            n.*,
            c.business_name,
            CASE 
                WHEN n.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'جديد'
                WHEN n.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 'اليوم'
                WHEN n.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 'هذا الأسبوع'
                ELSE 'قديم'
            END as time_category,
            DATE_FORMAT(n.created_at, '%Y-%m-%d %H:%i') as formatted_date
        FROM admin_notifications n
        LEFT JOIN clients c ON n.client_id = c.client_id
        {$where_clause}
        ORDER BY n.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب عدد الإشعارات غير المقروءة
    $unread_query = "
        SELECT COUNT(*) as unread_count
        FROM admin_notifications
        WHERE is_read = 0 AND is_dismissed = 0
    ";
    if ($client_id) {
        $unread_query .= " AND client_id = ?";
        $unread_stmt = $pdo->prepare($unread_query);
        $unread_stmt->execute([$client_id]);
    } else {
        $unread_stmt = $pdo->query($unread_query);
    }
    $unread_count = $unread_stmt->fetch(PDO::FETCH_ASSOC)['unread_count'];
    
    // تحديد أيقونات الإشعارات
    foreach ($notifications as &$notification) {
        switch ($notification['notification_type']) {
            case 'shift_completed':
                $notification['icon'] = 'fas fa-play-circle';
                $notification['color'] = 'success';
                break;
            case 'shift_summary_ready':
                $notification['icon'] = 'fas fa-stop-circle';
                $notification['color'] = 'info';
                break;
            case 'critical_event':
                $notification['icon'] = 'fas fa-exclamation-triangle';
                $notification['color'] = 'danger';
                break;
            case 'attendance_alert':
                $notification['icon'] = 'fas fa-user-clock';
                $notification['color'] = 'warning';
                break;
            case 'performance_alert':
                $notification['icon'] = 'fas fa-chart-line';
                $notification['color'] = 'primary';
                break;
            default:
                $notification['icon'] = 'fas fa-bell';
                $notification['color'] = 'secondary';
        }
    }
    
    echo json_encode([
        'success' => true,
        'notifications' => $notifications,
        'unread_count' => $unread_count,
        'total_fetched' => count($notifications)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في جلب الإشعارات: ' . $e->getMessage()
    ]);
}
?>
