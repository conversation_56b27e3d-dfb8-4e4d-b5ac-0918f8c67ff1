<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

header('Content-Type: application/json');

// التحقق من تسجيل دخول الموظف
if (!isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح بالوصول']);
    exit;
}

$employee_id = $_SESSION['employee_id'];

// التحقق من البيانات المطلوبة
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مسموحة']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['activity_type']) || !isset($input['activity_description'])) {
    http_response_code(400);
    echo json_encode(['error' => 'نوع النشاط والوصف مطلوبان']);
    exit;
}

try {
    // البحث عن الشيفت النشط للموظف
    $stmt = $pdo->prepare("
        SELECT sa.shift_id
        FROM shift_attendance sa 
        JOIN shifts s ON sa.shift_id = s.shift_id 
        WHERE sa.employee_id = ? 
        AND sa.check_out_time IS NULL 
        AND s.status = 'active'
        LIMIT 1
    ");
    $stmt->execute([$employee_id]);
    $active_shift = $stmt->fetch();
    
    if (!$active_shift) {
        http_response_code(400);
        echo json_encode(['error' => 'لا يوجد شيفت نشط']);
        exit;
    }
    
    // تسجيل النشاط
    $stmt = $pdo->prepare("
        INSERT INTO shift_activities (
            shift_id, 
            employee_id, 
            activity_type, 
            activity_description, 
            related_id, 
            related_table, 
            amount, 
            notes,
            activity_time
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        $active_shift['shift_id'],
        $employee_id,
        $input['activity_type'],
        $input['activity_description'],
        $input['related_id'] ?? null,
        $input['related_table'] ?? null,
        $input['amount'] ?? 0,
        $input['notes'] ?? null
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم تسجيل النشاط بنجاح',
        'activity_id' => $pdo->lastInsertId()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'حدث خطأ في تسجيل النشاط: ' . $e->getMessage()]);
}

/**
 * دالة مساعدة لتسجيل نشاط تلقائياً
 * يمكن استدعاؤها من ملفات أخرى
 */
function logShiftActivity($pdo, $employee_id, $activity_type, $description, $related_id = null, $related_table = null, $amount = 0, $notes = null) {
    try {
        // البحث عن الشيفت النشط
        $stmt = $pdo->prepare("
            SELECT sa.shift_id
            FROM shift_attendance sa 
            JOIN shifts s ON sa.shift_id = s.shift_id 
            WHERE sa.employee_id = ? 
            AND sa.check_out_time IS NULL 
            AND s.status = 'active'
            LIMIT 1
        ");
        $stmt->execute([$employee_id]);
        $active_shift = $stmt->fetch();
        
        if ($active_shift) {
            $stmt = $pdo->prepare("
                INSERT INTO shift_activities (
                    shift_id, employee_id, activity_type, activity_description, 
                    related_id, related_table, amount, notes, activity_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $active_shift['shift_id'],
                $employee_id,
                $activity_type,
                $description,
                $related_id,
                $related_table,
                $amount,
                $notes
            ]);
            
            return true;
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("خطأ في تسجيل نشاط الشيفت: " . $e->getMessage());
        return false;
    }
}
?>
