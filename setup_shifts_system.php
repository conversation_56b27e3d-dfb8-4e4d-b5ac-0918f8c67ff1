<?php
/**
 * إعداد نظام الشيفتات
 * تاريخ الإنشاء: 27 سبتمبر 2025
 */

require_once 'config/database.php';

try {
    echo "<h2>إعداد نظام الشيفتات</h2>";
    echo "<div style='font-family: Arial; direction: rtl; text-align: right;'>";
    
    // قراءة ملف SQL
    $sql_file = 'create_shifts_system.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ملف SQL غير موجود: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    if ($sql_content === false) {
        throw new Exception("فشل في قراءة ملف SQL");
    }
    
    echo "<p>✓ تم قراءة ملف SQL بنجاح</p>";
    
    // تقسيم الاستعلامات
    $statements = explode(';', $sql_content);
    $executed = 0;
    $errors = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $executed++;
            
            // عرض نوع الاستعلام المنفذ
            if (stripos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`([^`]+)`/i', $statement, $matches);
                if (isset($matches[1])) {
                    echo "<p>✓ تم إنشاء جدول: {$matches[1]}</p>";
                }
            } elseif (stripos($statement, 'CREATE VIEW') !== false) {
                preg_match('/CREATE.*?VIEW.*?`([^`]+)`/i', $statement, $matches);
                if (isset($matches[1])) {
                    echo "<p>✓ تم إنشاء عرض: {$matches[1]}</p>";
                }
            } elseif (stripos($statement, 'CREATE PROCEDURE') !== false) {
                preg_match('/CREATE PROCEDURE.*?`([^`]+)`/i', $statement, $matches);
                if (isset($matches[1])) {
                    echo "<p>✓ تم إنشاء إجراء مخزن: {$matches[1]}</p>";
                }
            } elseif (stripos($statement, 'INSERT') !== false) {
                echo "<p>✓ تم إدراج البيانات الافتراضية</p>";
            } elseif (stripos($statement, 'CREATE INDEX') !== false) {
                echo "<p>✓ تم إنشاء فهرس</p>";
            }
            
        } catch (PDOException $e) {
            $errors++;
            echo "<p style='color: red;'>✗ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "</p>";
            echo "<pre style='background: #f5f5f5; padding: 10px; font-size: 12px;'>" . htmlspecialchars(substr($statement, 0, 200)) . "...</pre>";
        }
    }
    
    echo "<hr>";
    echo "<h3>ملخص التنفيذ:</h3>";
    echo "<p><strong>الاستعلامات المنفذة بنجاح:</strong> $executed</p>";
    echo "<p><strong>الأخطاء:</strong> $errors</p>";
    
    if ($errors == 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ تم إعداد نظام الشيفتات بنجاح!</p>";
        
        // التحقق من الجداول المنشأة
        echo "<h3>التحقق من الجداول المنشأة:</h3>";
        $tables = ['shifts', 'shift_attendance', 'shift_activities', 'shift_reports', 'shift_settings'];
        
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    echo "<p>✓ جدول $table موجود</p>";
                    
                    // عرض عدد الأعمدة
                    $stmt = $pdo->query("DESCRIBE $table");
                    $columns = $stmt->rowCount();
                    echo "<p style='margin-right: 20px;'>عدد الأعمدة: $columns</p>";
                } else {
                    echo "<p style='color: red;'>✗ جدول $table غير موجود</p>";
                }
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ خطأ في فحص جدول $table: " . $e->getMessage() . "</p>";
            }
        }
        
        // التحقق من الـ Views
        echo "<h3>التحقق من العروض (Views):</h3>";
        try {
            $stmt = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'VIEW'");
            $views = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (in_array('active_shifts_view', $views)) {
                echo "<p>✓ عرض active_shifts_view موجود</p>";
            } else {
                echo "<p style='color: orange;'>⚠ عرض active_shifts_view غير موجود</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ خطأ في فحص العروض: " . $e->getMessage() . "</p>";
        }
        
        // التحقق من الإجراءات المخزنة
        echo "<h3>التحقق من الإجراءات المخزنة:</h3>";
        try {
            $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Db = DATABASE()");
            $procedures = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);
            
            $expected_procedures = ['StartShift', 'EndShift'];
            foreach ($expected_procedures as $proc) {
                if (in_array($proc, $procedures)) {
                    echo "<p>✓ إجراء $proc موجود</p>";
                } else {
                    echo "<p style='color: orange;'>⚠ إجراء $proc غير موجود</p>";
                }
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ خطأ في فحص الإجراءات المخزنة: " . $e->getMessage() . "</p>";
        }
        
        // إنشاء بيانات تجريبية (اختيارية)
        echo "<h3>إنشاء بيانات تجريبية:</h3>";
        try {
            // التحقق من وجود موظفين
            $stmt = $pdo->query("SELECT COUNT(*) FROM employees WHERE is_active = 1");
            $employee_count = $stmt->fetchColumn();
            
            if ($employee_count > 0) {
                echo "<p>✓ يوجد $employee_count موظف نشط في النظام</p>";
                echo "<p>يمكنك الآن إنشاء شيفتات للموظفين من خلال واجهة النظام</p>";
            } else {
                echo "<p style='color: orange;'>⚠ لا يوجد موظفين في النظام. يرجى إضافة موظفين أولاً</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ خطأ في فحص الموظفين: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: red; font-weight: bold;'>✗ حدثت أخطاء أثناء الإعداد. يرجى مراجعة الأخطاء أعلاه.</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-family: Arial; direction: rtl; text-align: right;'>";
    echo "<h2>خطأ في إعداد نظام الشيفتات</h2>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

p {
    margin: 8px 0;
    padding: 5px;
}

hr {
    border: none;
    border-top: 2px solid #ecf0f1;
    margin: 20px 0;
}

pre {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    font-size: 12px;
    overflow-x: auto;
}
</style>
