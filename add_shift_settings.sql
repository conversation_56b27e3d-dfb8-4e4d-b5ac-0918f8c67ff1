-- إضا<PERSON>ة جدول إعدادات الشيفتات
-- تاريخ الإنشاء: 27 سبتمبر 2025

-- جدول إعدادات الشيفتات لكل عميل
CREATE TABLE IF NOT EXISTS `shift_settings` (
    `setting_id` int(11) NOT NULL AUTO_INCREMENT,
    `client_id` int(11) NOT NULL,
    `require_scheduled_shifts` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'هل يتطلب ورديات مجدولة مسبقاً',
    `allow_manual_shifts` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'السماح بالورديات اليدوية',
    `auto_shift_duration` int(11) DEFAULT 480 COMMENT 'مدة الوردية التلقائية بالدقائق (8 ساعات افتراضي)',
    `max_shift_duration` int(11) DEFAULT 720 COMMENT 'أقصى مدة للوردية بالدقائق (12 ساعة)',
    `require_break` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'هل تتطلب استراحة',
    `default_break_duration` int(11) DEFAULT 30 COMMENT 'مدة الاستراحة الافتراضية بالدقائق',
    `shift_overlap_allowed` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'السماح بتداخل الورديات',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`setting_id`),
    UNIQUE KEY `unique_client_settings` (`client_id`),
    KEY `idx_client_id` (`client_id`),
    CONSTRAINT `fk_shift_settings_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج إعدادات افتراضية لجميع العملاء الموجودين
INSERT IGNORE INTO `shift_settings` (`client_id`, `require_scheduled_shifts`, `allow_manual_shifts`)
SELECT `client_id`, 1, 0 FROM `clients`;

-- إضافة عمود لتتبع نوع الوردية في جدول shifts
ALTER TABLE `shifts` 
ADD COLUMN IF NOT EXISTS `shift_type` enum('scheduled','manual','auto') NOT NULL DEFAULT 'scheduled' COMMENT 'نوع الوردية',
ADD COLUMN IF NOT EXISTS `auto_created` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'تم إنشاؤها تلقائياً';

-- إضافة فهرس للبحث السريع
ALTER TABLE `shifts` ADD INDEX IF NOT EXISTS `idx_shift_type` (`shift_type`);
ALTER TABLE `shifts` ADD INDEX IF NOT EXISTS `idx_auto_created` (`auto_created`);
