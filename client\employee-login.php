<?php
require_once '../config/database.php';

// تضمين نظام الحماية المتقدم
require_once '../includes/secure_session_middleware.php';

if (!class_exists('SecurityManager')) {
    require_once '../includes/security.php';
}

if (!isset($security)) {
    $security = new SecurityManager($pdo);
}

/**
 * إزالة الموظف من قائمة الجلسات المنتهية
 */
function clearEmployeeFromInvalidatedSessions($employee_id) {
    $invalidated_sessions_file = '../temp/invalidated_sessions.json';

    if (!file_exists($invalidated_sessions_file)) {
        return;
    }

    try {
        $content = file_get_contents($invalidated_sessions_file);
        $invalidated_sessions = json_decode($content, true) ?: [];

        // إزالة الموظف من القائمة
        if (isset($invalidated_sessions[$employee_id])) {
            unset($invalidated_sessions[$employee_id]);

            // حفظ القائمة المحدثة
            file_put_contents($invalidated_sessions_file, json_encode($invalidated_sessions, JSON_PRETTY_PRINT));
        }

    } catch (Exception $e) {
        error_log('خطأ في إزالة الموظف من الجلسات المنتهية: ' . $e->getMessage());
    }
}

// التحقق من رسائل الخطأ في URL
$error_from_url = '';
if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'session_invalid':
            $error_from_url = 'تم إنهاء جلستك بسبب إلغاء تفعيل حساب المحل أو الموظف. يرجى تسجيل الدخول مرة أخرى.';
            break;
        case 'client_deactivated':
            $error_from_url = 'حساب المحل غير مفعل حالياً. يرجى التواصل مع الإدارة.';
            break;
    }
}

// Redirect to dashboard if already logged in and session is valid
if (isset($_SESSION['employee_id']) && !empty($_SESSION['employee_id'])) {
    // التحقق من صحة الجلسة قبل التوجيه
    require_once 'includes/employee-auth.php';
    if (isEmployeeSessionValid()) {
        header('Location: dashboard.php');
        exit;
    } else {
        // إنهاء الجلسة غير الصحيحة
        invalidateEmployeeSession('invalid_session_on_login');
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];
    $csrf_token = $_POST['csrf_token'] ?? '';

    if (!$security->validateCsrfToken($csrf_token, 'employee_login')) {
        $error = 'طلب غير صالح. يرجى المحاولة مرة أخرى.';
    } else {
    try {
        $stmt = $pdo->prepare("
            SELECT e.*, c.business_name, c.owner_name, c.is_active as client_active
            FROM employees e
            JOIN clients c ON e.client_id = c.client_id
            WHERE e.username = ? AND e.is_active = 1 AND c.is_active = 1
        ");
        $stmt->execute([$username]);
        $employee = $stmt->fetch();

        if ($employee && password_verify($password, $employee['password_hash'])) {
            // تحديث آخر تسجيل دخول
            $updateStmt = $pdo->prepare("
                UPDATE employees
                SET last_login = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $updateStmt->execute([$employee['id']]);

            // إزالة الموظف من قائمة الجلسات المنتهية (إذا كان موجوداً)
            try {
                clearEmployeeFromInvalidatedSessions($employee['id']);
            } catch (Exception $e) {
                // تجاهل الأخطاء في هذه الدالة
                error_log("تحذير: فشل في إزالة الموظف من قائمة الجلسات المنتهية: " . $e->getMessage());
            }

            // تخزين بيانات الموظف في الجلسة
            $_SESSION['employee_id'] = $employee['id'];
            $_SESSION['employee_name'] = $employee['name'];
            $_SESSION['employee_role'] = $employee['role'];
            $_SESSION['client_id'] = $employee['client_id'];
            $_SESSION['business_name'] = $employee['business_name'];
            $_SESSION['owner_name'] = $employee['owner_name'];

            // محاولة إنشاء جلسة آمنة (اختياري)
            try {
                // createSecureUserSession - معطل مؤقتاً
            } catch (Exception $e) {
                error_log("تحذير: فشل في إنشاء جلسة آمنة: " . $e->getMessage());
            }

            // إضافة رسالة ترحيب للموظف الجديد
            $_SESSION['success'] = "مرحباً " . $employee['name'] . "! يمكنك الآن فتح الوردية لبدء العمل.";

            header('Location: dashboard.php');
            exit;
        } else {
            // التحقق من سبب فشل تسجيل الدخول
            $checkStmt = $pdo->prepare("
                SELECT e.*, c.is_active as client_active
                FROM employees e
                JOIN clients c ON e.client_id = c.client_id
                WHERE e.username = ?
            ");
            $checkStmt->execute([$username]);
            $checkEmployee = $checkStmt->fetch();

            if ($checkEmployee) {
                if (!$checkEmployee['is_active']) {
                    $error = "حساب الموظف غير مفعل. يرجى التواصل مع الإدارة";
                } elseif (!$checkEmployee['client_active']) {
                    $error = "حساب المحل غير مفعل حالياً. يرجى التواصل مع الإدارة";
                } elseif (!password_verify($password, $checkEmployee['password_hash'])) {
                    $error = "كلمة المرور غير صحيحة";
                } else {
                    $error = "اسم المستخدم أو كلمة المرور غير صحيحة";
                }
            } else {
                $error = "اسم المستخدم غير موجود";
            }
        }
    } catch (PDOException $e) {
        $error = "حدث خطأ في النظام";
    }
    }
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دخول الموظف - PlayGood</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/auth.css" rel="stylesheet">
    <link href="../assets/css/auth-modern.css" rel="stylesheet">
    <link href="../assets/css/auth-spacing.css" rel="stylesheet">
    <link href="../assets/css/auth-enhanced.css" rel="stylesheet">
    <link href="../assets/css/auth-animations.css" rel="stylesheet">
    <link href="../assets/css/auth-minimal.css" rel="stylesheet">
    <link href="../assets/css/auth-animations.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/no-shake.css" rel="stylesheet">
</head>
<body class="auth-page">
    <!-- تأثيرات الخلفية -->
    <div class="auth-particles"></div>

    <div class="container auth-container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-5 col-lg-4">
                <div class="auth-card auth-card-enhanced">
                    <div class="auth-card-header">
                        <div class="auth-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <h3 class="auth-title">دخول الموظف</h3>
                        <p class="auth-subtitle">مرحباً بك في نظام PlayGood الاحترافي</p>
                    </div>

                    <div class="auth-card-body">
                        <?php if (isset($error)): ?>
                            <div class="auth-alert auth-alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($error_from_url)): ?>
                            <div class="auth-alert auth-alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error_from_url; ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="" id="employeeLoginForm">
                            <input type="hidden" name="csrf_token" value="<?php echo $security->generateCsrfToken('employee_login'); ?>">
                            <div class="auth-form-group">
                                <input type="text" name="username" class="auth-form-control" placeholder=" " required>
                                <i class="fas fa-user auth-form-icon"></i>
                                <label class="auth-form-label">اسم المستخدم</label>
                            </div>

                            <div class="auth-form-group">
                                <input type="password" name="password" class="auth-form-control" placeholder=" " required>
                                <i class="fas fa-lock auth-form-icon"></i>
                                <label class="auth-form-label">كلمة المرور</label>
                            </div>

                            <button type="submit" class="auth-btn auth-btn-modern">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </form>

                        <div class="text-center mt-4">
                            <a href="reset-password.php" class="auth-link">
                                <i class="fas fa-key me-1"></i>
                                نسيت كلمة المرور؟
                            </a>
                        </div>

                        <!-- روابط إضافية -->
                        <div class="text-center mt-5 pt-4" style="border-top: 1px solid rgba(102, 126, 234, 0.1);">
                            <p class="text-muted small mb-3" style="font-weight: 500;">أو تسجيل الدخول كـ</p>
                            <div class="d-flex justify-content-center gap-4 flex-wrap">
                               <!-- <a href="../admin/login.php" class="auth-link-alt">
                                    <i class="fas fa-user-shield"></i>
                                    <span>مدير النظام</span>
                                </a>-->
                                <a href="login.php" class="auth-link-alt">
                                    <i class="fas fa-store"></i>
                                    <span>صاحب المحل</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="text-center mt-4">
                    <p class="text-white-50 small">
                        <i class="fas fa-shield-alt me-1"></i>
                        جميع البيانات محمية ومشفرة
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/auth-effects.js"></script>
    <script>
        // تحسين تجربة المستخدم المتقدم
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('employeeLoginForm');
            const submitBtn = form.querySelector('.auth-btn');
            const originalText = submitBtn.innerHTML;

            // تأثير التحميل المحسن مع رسائل متنوعة
            const loadingMessages = [
                'جاري تسجيل الدخول...',
                'جاري التحقق من صلاحيات الموظف...',
                'جاري التحقق من حالة المحل...',
                'تم التحقق، جاري التوجيه...'
            ];

            form.addEventListener('submit', function() {
                submitBtn.classList.add('auth-loading');
                submitBtn.disabled = true;

                let messageIndex = 0;
                const messageInterval = setInterval(() => {
                    if (messageIndex < loadingMessages.length) {
                        submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${loadingMessages[messageIndex]}`;
                        messageIndex++;
                    } else {
                        clearInterval(messageInterval);
                    }
                }, 900);
            });

            // إضافة تأثيرات للحقول
            const inputs = document.querySelectorAll('.auth-form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });
            });
        });
    </script>
</body>
</html>