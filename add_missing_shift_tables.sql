-- إضافة الجداول المفقودة لنظام الشيفتات
-- تاريخ الإنشاء: 27 سبتمبر 2025

-- جدول أنشطة الشيفت
CREATE TABLE IF NOT EXISTS `shift_activities` (
    `activity_id` int(11) NOT NULL AUTO_INCREMENT,
    `shift_id` int(11) NOT NULL,
    `employee_id` int(11) NOT NULL,
    `activity_type` enum('session_start', 'session_end', 'sale', 'customer_service', 'maintenance', 'cleaning', 'break', 'other') NOT NULL,
    `activity_description` text NOT NULL,
    `related_id` int(11) NULL COMMENT 'معرف العنصر المرتبط (جلسة، منتج، إلخ)',
    `related_table` varchar(50) NULL COMMENT 'اسم الجدول المرتبط',
    `amount` decimal(10,2) DEFAULT 0.00 COMMENT 'المبلغ المرتبط بالنشاط',
    `activity_time` datetime NOT NULL DEFAULT current_timestamp(),
    `notes` text NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`activity_id`),
    KEY `idx_shift_employee` (`shift_id`, `employee_id`),
    KEY `idx_activity_type` (`activity_type`),
    KEY `idx_activity_time` (`activity_time`),
    KEY `idx_related` (`related_table`, `related_id`),
    FOREIGN KEY (`shift_id`) REFERENCES `shifts`(`shift_id`) ON DELETE CASCADE,
    FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول تقارير الشيفتات
CREATE TABLE IF NOT EXISTS `shift_reports` (
    `report_id` int(11) NOT NULL AUTO_INCREMENT,
    `shift_id` int(11) NOT NULL,
    `employee_id` int(11) NOT NULL,
    `total_sessions` int(11) DEFAULT 0,
    `total_sales` decimal(10,2) DEFAULT 0.00,
    `total_revenue` decimal(10,2) DEFAULT 0.00,
    `customers_served` int(11) DEFAULT 0,
    `products_sold` int(11) DEFAULT 0,
    `average_session_duration` decimal(5,2) DEFAULT 0.00 COMMENT 'متوسط مدة الجلسة بالساعات',
    `performance_score` decimal(3,2) DEFAULT 0.00 COMMENT 'نقاط الأداء من 10',
    `issues_reported` int(11) DEFAULT 0,
    `customer_complaints` int(11) DEFAULT 0,
    `summary_notes` text NULL,
    `generated_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `generated_by` int(11) NULL COMMENT 'معرف من أنشأ التقرير',
    PRIMARY KEY (`report_id`),
    UNIQUE KEY `unique_shift_report` (`shift_id`, `employee_id`),
    KEY `idx_employee_date` (`employee_id`, `generated_at`),
    KEY `idx_performance` (`performance_score`),
    FOREIGN KEY (`shift_id`) REFERENCES `shifts`(`shift_id`) ON DELETE CASCADE,
    FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX IF NOT EXISTS `idx_activities_recent` ON `shift_activities` (`activity_time` DESC);

-- إنشاء view مفيد للشيفتات النشطة
CREATE OR REPLACE VIEW `active_shifts_view` AS
SELECT 
    s.shift_id,
    s.client_id,
    s.employee_id,
    e.name as employee_name,
    e.role as employee_role,
    c.business_name,
    s.shift_name,
    s.shift_date,
    s.start_time as scheduled_start,
    s.end_time as scheduled_end,
    s.actual_start_time as actual_start,
    s.status,
    sa.check_in_time,
    sa.status as attendance_status,
    TIMESTAMPDIFF(MINUTE, sa.check_in_time, NOW()) as minutes_worked
FROM shifts s
JOIN employees e ON s.employee_id = e.id
JOIN clients c ON s.client_id = c.client_id
LEFT JOIN shift_attendance sa ON s.shift_id = sa.shift_id
WHERE s.status IN ('active', 'scheduled')
ORDER BY s.shift_date DESC, s.start_time ASC;

-- إنشاء view لتقارير الشيفتات
CREATE OR REPLACE VIEW `shift_reports_view` AS
SELECT 
    sr.report_id,
    sr.shift_id,
    s.shift_name,
    s.shift_date,
    e.name as employee_name,
    e.role as employee_role,
    c.business_name,
    sr.total_sessions,
    sr.total_sales,
    sr.total_revenue,
    sr.customers_served,
    sr.products_sold,
    sr.average_session_duration,
    sr.performance_score,
    sr.issues_reported,
    sr.customer_complaints,
    sr.generated_at,
    TIMESTAMPDIFF(HOUR, s.actual_start_time, s.actual_end_time) as shift_duration_hours
FROM shift_reports sr
JOIN shifts s ON sr.shift_id = s.shift_id
JOIN employees e ON sr.employee_id = e.id
JOIN clients c ON s.client_id = c.client_id
ORDER BY sr.generated_at DESC;

-- إضافة triggers لتسجيل الأنشطة تلقائياً
DELIMITER //

CREATE TRIGGER IF NOT EXISTS `log_session_start` 
AFTER INSERT ON `sessions` 
FOR EACH ROW
BEGIN
    DECLARE current_shift_id INT;
    
    -- البحث عن الشيفت النشط للموظف
    SELECT s.shift_id INTO current_shift_id
    FROM shifts s
    JOIN shift_attendance sa ON s.shift_id = sa.shift_id
    WHERE s.employee_id = NEW.employee_id 
    AND s.status = 'active'
    AND sa.status = 'checked_in'
    AND s.shift_date = CURDATE()
    LIMIT 1;
    
    -- تسجيل النشاط إذا وُجد شيفت نشط
    IF current_shift_id IS NOT NULL THEN
        INSERT INTO shift_activities 
        (shift_id, employee_id, activity_type, activity_description, related_id, related_table, amount, activity_time)
        VALUES 
        (current_shift_id, NEW.employee_id, 'session_start', 
         CONCAT('بدء جلسة للعميل: ', NEW.customer_name, ' على الجهاز: ', NEW.device_name), 
         NEW.session_id, 'sessions', NEW.total_cost, NEW.start_time);
    END IF;
END //

CREATE TRIGGER IF NOT EXISTS `log_session_end` 
AFTER UPDATE ON `sessions` 
FOR EACH ROW
BEGIN
    DECLARE current_shift_id INT;
    
    -- التحقق من إنهاء الجلسة
    IF OLD.status != 'completed' AND NEW.status = 'completed' THEN
        -- البحث عن الشيفت النشط للموظف
        SELECT s.shift_id INTO current_shift_id
        FROM shifts s
        JOIN shift_attendance sa ON s.shift_id = sa.shift_id
        WHERE s.employee_id = NEW.employee_id 
        AND s.status = 'active'
        AND sa.status = 'checked_in'
        AND s.shift_date = CURDATE()
        LIMIT 1;
        
        -- تسجيل النشاط إذا وُجد شيفت نشط
        IF current_shift_id IS NOT NULL THEN
            INSERT INTO shift_activities 
            (shift_id, employee_id, activity_type, activity_description, related_id, related_table, amount, activity_time)
            VALUES 
            (current_shift_id, NEW.employee_id, 'session_end', 
             CONCAT('إنهاء جلسة للعميل: ', NEW.customer_name, ' المدة: ', 
                    TIMESTAMPDIFF(MINUTE, NEW.start_time, NEW.end_time), ' دقيقة'), 
             NEW.session_id, 'sessions', NEW.total_cost, NEW.end_time);
        END IF;
    END IF;
END //

CREATE TRIGGER IF NOT EXISTS `log_order_sale` 
AFTER INSERT ON `orders` 
FOR EACH ROW
BEGIN
    DECLARE current_shift_id INT;
    DECLARE current_employee_id INT;
    
    -- الحصول على معرف الموظف من الجلسة إذا كان الطلب مرتبط بجلسة
    IF NEW.session_id IS NOT NULL THEN
        SELECT employee_id INTO current_employee_id
        FROM sessions 
        WHERE session_id = NEW.session_id;
        
        -- البحث عن الشيفت النشط للموظف
        SELECT s.shift_id INTO current_shift_id
        FROM shifts s
        JOIN shift_attendance sa ON s.shift_id = sa.shift_id
        WHERE s.employee_id = current_employee_id 
        AND s.status = 'active'
        AND sa.status = 'checked_in'
        AND s.shift_date = CURDATE()
        LIMIT 1;
        
        -- تسجيل النشاط إذا وُجد شيفت نشط
        IF current_shift_id IS NOT NULL THEN
            INSERT INTO shift_activities 
            (shift_id, employee_id, activity_type, activity_description, related_id, related_table, amount, activity_time)
            VALUES 
            (current_shift_id, current_employee_id, 'sale', 
             CONCAT('بيع منتجات بقيمة: ', NEW.total_amount, ' ريال'), 
             NEW.order_id, 'orders', NEW.total_amount, NEW.created_at);
        END IF;
    END IF;
END //

DELIMITER ;

-- إدراج بيانات تجريبية للاختبار (اختيارية)
-- INSERT INTO shift_activities (shift_id, employee_id, activity_type, activity_description, activity_time)
-- SELECT 1, 1, 'other', 'نشاط تجريبي', NOW()
-- WHERE EXISTS (SELECT 1 FROM shifts WHERE shift_id = 1);
