/**
 * نظام تتبع أنشطة الشيفت
 * يقوم بتسجيل الأنشطة تلقائياً أثناء الشيفت
 */

class ShiftTracker {
    constructor() {
        this.isActive = false;
        this.checkShiftStatus();
        this.initEventListeners();
    }

    // فحص حالة الشيفت
    async checkShiftStatus() {
        try {
            const response = await fetch('api/shift_status_check.php');
            const data = await response.json();
            
            if (data.success && data.active_shift) {
                this.isActive = true;
                this.showShiftIndicator(data.active_shift);
            } else {
                this.isActive = false;
                this.hideShiftIndicator();
            }
        } catch (error) {
            console.error('خطأ في فحص حالة الشيفت:', error);
        }
    }

    // عرض مؤشر الشيفت النشط
    showShiftIndicator(shiftData) {
        // إزالة المؤشر السابق إن وجد
        const existingIndicator = document.getElementById('shift-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // إنشاء مؤشر جديد
        const indicator = document.createElement('div');
        indicator.id = 'shift-indicator';
        indicator.className = 'shift-indicator active';
        indicator.innerHTML = `
            <div class="shift-info">
                <i class="fas fa-clock"></i>
                <span class="shift-name">${shiftData.shift_name}</span>
                <span class="shift-time" id="shift-timer">${this.formatDuration(shiftData.minutes_worked)}</span>
            </div>
            <div class="shift-controls">
                <button class="btn btn-sm btn-outline-light" onclick="window.location.href='shift_control.php'">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        `;

        // إضافة المؤشر إلى الصفحة
        document.body.appendChild(indicator);

        // تحديث المؤقت كل دقيقة
        this.startTimer(shiftData.minutes_worked);
    }

    // إخفاء مؤشر الشيفت
    hideShiftIndicator() {
        const indicator = document.getElementById('shift-indicator');
        if (indicator) {
            indicator.remove();
        }
        
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
    }

    // بدء المؤقت
    startTimer(initialMinutes) {
        let minutes = initialMinutes;
        
        this.timerInterval = setInterval(() => {
            minutes++;
            const timerElement = document.getElementById('shift-timer');
            if (timerElement) {
                timerElement.textContent = this.formatDuration(minutes);
            }
        }, 60000); // كل دقيقة
    }

    // تنسيق المدة
    formatDuration(totalMinutes) {
        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;
        return `${hours}س ${minutes}د`;
    }

    // تهيئة مستمعي الأحداث
    initEventListeners() {
        // تسجيل بدء الجلسات
        this.observeSessionStart();
        
        // تسجيل إنهاء الجلسات
        this.observeSessionEnd();
        
        // تسجيل المبيعات
        this.observeSales();
        
        // تسجيل خدمة العملاء
        this.observeCustomerService();
    }

    // مراقبة بدء الجلسات
    observeSessionStart() {
        // البحث عن نماذج بدء الجلسة
        const sessionForms = document.querySelectorAll('form[action*="sessions"], form[id*="session"]');
        
        sessionForms.forEach(form => {
            form.addEventListener('submit', (e) => {
                // التحقق من أن النموذج لبدء جلسة
                const submitButton = form.querySelector('button[type="submit"]');
                if (submitButton && submitButton.textContent.includes('بدء')) {
                    setTimeout(() => {
                        this.logActivity('session_start', 'بدء جلسة جديدة');
                    }, 1000);
                }
            });
        });
    }

    // مراقبة إنهاء الجلسات
    observeSessionEnd() {
        // البحث عن أزرار إنهاء الجلسة
        document.addEventListener('click', (e) => {
            if (e.target.matches('button[onclick*="end"], button[onclick*="إنهاء"], .end-session-btn')) {
                setTimeout(() => {
                    this.logActivity('session_end', 'إنهاء جلسة');
                }, 1000);
            }
        });
    }

    // مراقبة المبيعات
    observeSales() {
        // البحث عن نماذج الطلبات
        const orderForms = document.querySelectorAll('form[action*="order"], form[id*="order"]');
        
        orderForms.forEach(form => {
            form.addEventListener('submit', (e) => {
                setTimeout(() => {
                    this.logActivity('sale', 'تم إنشاء طلب جديد');
                }, 1000);
            });
        });
    }

    // مراقبة خدمة العملاء
    observeCustomerService() {
        // البحث عن أنشطة خدمة العملاء
        document.addEventListener('click', (e) => {
            if (e.target.matches('button[onclick*="customer"], .customer-service-btn')) {
                this.logActivity('customer_service', 'تقديم خدمة عملاء');
            }
        });
    }

    // تسجيل نشاط
    async logActivity(type, description, relatedId = null, relatedTable = null, amount = 0, notes = null) {
        if (!this.isActive) {
            return; // لا يوجد شيفت نشط
        }

        try {
            const response = await fetch('api/log_shift_activity.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    activity_type: type,
                    activity_description: description,
                    related_id: relatedId,
                    related_table: relatedTable,
                    amount: amount,
                    notes: notes
                })
            });

            const data = await response.json();
            
            if (!data.success) {
                console.warn('فشل في تسجيل النشاط:', data.error);
            }
        } catch (error) {
            console.error('خطأ في تسجيل النشاط:', error);
        }
    }

    // تسجيل نشاط يدوي
    async logManualActivity(type, description, notes = null) {
        return await this.logActivity(type, description, null, null, 0, notes);
    }

    // تسجيل استراحة
    async logBreak(duration = null) {
        const description = duration ? `استراحة لمدة ${duration} دقيقة` : 'بدء استراحة';
        return await this.logActivity('break', description);
    }

    // تسجيل صيانة
    async logMaintenance(deviceName, issue) {
        const description = `صيانة ${deviceName}: ${issue}`;
        return await this.logActivity('maintenance', description);
    }

    // تسجيل تنظيف
    async logCleaning(area) {
        const description = `تنظيف ${area}`;
        return await this.logActivity('cleaning', description);
    }
}

// تهيئة نظام تتبع الشيفت عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من أن المستخدم موظف
    if (document.body.dataset.userType === 'employee') {
        window.shiftTracker = new ShiftTracker();
        
        // فحص حالة الشيفت كل 5 دقائق
        setInterval(() => {
            window.shiftTracker.checkShiftStatus();
        }, 300000);
    }
});

// إضافة CSS للمؤشر
const shiftIndicatorCSS = `
.shift-indicator {
    position: fixed;
    top: 70px;
    right: 20px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 10px 15px;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 14px;
    font-weight: 500;
    animation: slideInRight 0.3s ease-out;
}

.shift-indicator.active {
    animation: pulse 2s infinite;
}

.shift-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.shift-name {
    font-weight: bold;
}

.shift-time {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.shift-controls button {
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transition: all 0.2s;
}

.shift-controls button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    50% {
        box-shadow: 0 4px 20px rgba(40, 167, 69, 0.5);
    }
}

@media (max-width: 768px) {
    .shift-indicator {
        right: 10px;
        top: 60px;
        padding: 8px 12px;
        font-size: 12px;
    }
    
    .shift-info {
        gap: 6px;
    }
    
    .shift-time {
        font-size: 11px;
        padding: 1px 6px;
    }
}
`;

// إضافة CSS إلى الصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = shiftIndicatorCSS;
document.head.appendChild(styleSheet);
