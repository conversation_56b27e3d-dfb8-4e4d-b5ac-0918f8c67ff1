<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/auth.php';

if (!isset($_GET['report_id'])) {
    http_response_code(400);
    echo "معرف التقرير مطلوب";
    exit;
}

$attendance_id = intval($_GET['report_id']); // تم تغيير المعامل ليكون attendance_id

try {
    // جلب تفاصيل التقرير
    $stmt = $pdo->prepare("
        SELECT
            sa.attendance_id,
            sa.shift_id,
            sa.employee_id,
            sa.check_in_time,
            sa.check_out_time,
            sa.notes as attendance_notes,
            sa.total_hours,
            TIMESTAMPDIFF(MINUTE, sa.check_in_time, sa.check_out_time) as total_minutes_worked,
            s.shift_name,
            s.shift_date,
            s.start_time,
            s.end_time,
            s.break_duration,
            e.name as employee_name,
            e.role as employee_role,
            c.business_name,
            COALESCE(sr.total_sessions, 0) as total_sessions,
            COALESCE(sr.total_sales, 0) as total_sales,
            COALESCE(sr.total_revenue, 0) as total_revenue,
            COALESCE(sr.customers_served, 0) as customers_served,
            COALESCE(sr.products_sold, 0) as products_sold,
            COALESCE(sr.average_session_duration, 0) as average_session_duration,
            COALESCE(sr.performance_score, 0) as performance_score,
            COALESCE(sr.issues_reported, 0) as issues_reported,
            COALESCE(sr.customer_complaints, 0) as customer_complaints,
            sr.summary_notes,
            sr.generated_at
        FROM shift_attendance sa
        JOIN shifts s ON sa.shift_id = s.shift_id
        JOIN employees e ON sa.employee_id = e.id
        JOIN clients c ON s.client_id = c.client_id
        LEFT JOIN shift_reports sr ON sa.shift_id = sr.shift_id AND sa.employee_id = sr.employee_id
        WHERE sa.attendance_id = ?
    ");
    $stmt->execute([$attendance_id]);
    $report = $stmt->fetch();
    
    if (!$report) {
        http_response_code(404);
        echo "التقرير غير موجود";
        exit;
    }
    
    // جلب أنشطة الشيفت
    $stmt = $pdo->prepare("
        SELECT 
            activity_type,
            activity_description,
            activity_time,
            amount,
            notes
        FROM shift_activities 
        WHERE shift_id = ? AND employee_id = ?
        ORDER BY activity_time ASC
    ");
    $stmt->execute([$report['shift_id'], $report['employee_id']]);
    $activities = $stmt->fetchAll();
    
    // حساب الإحصائيات الإضافية
    $hours_worked = $report['total_minutes_worked'] ? round($report['total_minutes_worked'] / 60, 2) : 0;
    $scheduled_hours = (strtotime($report['end_time']) - strtotime($report['start_time'])) / 3600;
    $efficiency = $scheduled_hours > 0 ? round(($hours_worked / $scheduled_hours) * 100, 1) : 0;
    
    ?>
    
    <div class="row">
        <div class="col-md-6">
            <h6 class="text-primary">معلومات الشيفت</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>اسم الشيفت:</strong></td>
                    <td><?php echo htmlspecialchars($report['shift_name']); ?></td>
                </tr>
                <tr>
                    <td><strong>التاريخ:</strong></td>
                    <td><?php echo date('Y-m-d', strtotime($report['shift_date'])); ?></td>
                </tr>
                <tr>
                    <td><strong>الموظف:</strong></td>
                    <td><?php echo htmlspecialchars($report['employee_name'] . ' - ' . $report['employee_role']); ?></td>
                </tr>
                <tr>
                    <td><strong>المحل:</strong></td>
                    <td><?php echo htmlspecialchars($report['business_name']); ?></td>
                </tr>
                <tr>
                    <td><strong>الوقت المجدول:</strong></td>
                    <td><?php echo date('H:i', strtotime($report['start_time'])) . ' - ' . date('H:i', strtotime($report['end_time'])); ?></td>
                </tr>
                <tr>
                    <td><strong>الوقت الفعلي:</strong></td>
                    <td>
                        <?php if ($report['check_in_time'] && $report['check_out_time']): ?>
                            <?php echo date('H:i', strtotime($report['check_in_time'])) . ' - ' . date('H:i', strtotime($report['check_out_time'])); ?>
                        <?php else: ?>
                            <span class="text-warning">غير مكتمل</span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td><strong>ساعات العمل:</strong></td>
                    <td>
                        <span class="badge bg-info"><?php echo $hours_worked; ?> ساعة</span>
                        <small class="text-muted">(كفاءة: <?php echo $efficiency; ?>%)</small>
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="col-md-6">
            <h6 class="text-success">إحصائيات الأداء</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>إجمالي الجلسات:</strong></td>
                    <td><span class="badge bg-primary"><?php echo $report['total_sessions']; ?></span></td>
                </tr>
                <tr>
                    <td><strong>العملاء المخدومين:</strong></td>
                    <td><span class="badge bg-info"><?php echo $report['customers_served']; ?></span></td>
                </tr>
                <tr>
                    <td><strong>إجمالي الإيرادات:</strong></td>
                    <td><span class="badge bg-success"><?php echo number_format($report['total_revenue'], 2); ?> ريال</span></td>
                </tr>
                <tr>
                    <td><strong>إجمالي المبيعات:</strong></td>
                    <td><span class="badge bg-warning"><?php echo number_format($report['total_sales'], 2); ?> ريال</span></td>
                </tr>
                <tr>
                    <td><strong>المنتجات المباعة:</strong></td>
                    <td><span class="badge bg-secondary"><?php echo $report['products_sold']; ?></span></td>
                </tr>
                <tr>
                    <td><strong>متوسط مدة الجلسة:</strong></td>
                    <td><?php echo number_format($report['average_session_duration'], 1); ?> دقيقة</td>
                </tr>
                <tr>
                    <td><strong>نقاط الأداء:</strong></td>
                    <td>
                        <?php
                        $score = $report['performance_score'];
                        $color = $score >= 8 ? 'success' : ($score >= 6 ? 'warning' : 'danger');
                        ?>
                        <span class="badge bg-<?php echo $color; ?> fs-6"><?php echo number_format($score, 1); ?>/10</span>
                    </td>
                </tr>
                <tr>
                    <td><strong>المشاكل المبلغة:</strong></td>
                    <td>
                        <?php if ($report['issues_reported'] > 0): ?>
                            <span class="badge bg-danger"><?php echo $report['issues_reported']; ?></span>
                        <?php else: ?>
                            <span class="text-success">لا توجد</span>
                        <?php endif; ?>
                    </td>
                </tr>
            </table>
        </div>
    </div>
    
    <?php if ($report['summary_notes']): ?>
        <div class="row mt-3">
            <div class="col-12">
                <h6 class="text-info">ملاحظات التقرير</h6>
                <div class="alert alert-info">
                    <?php echo nl2br(htmlspecialchars($report['summary_notes'])); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if ($report['attendance_notes']): ?>
        <div class="row">
            <div class="col-12">
                <h6 class="text-warning">ملاحظات الحضور</h6>
                <div class="alert alert-warning">
                    <?php echo nl2br(htmlspecialchars($report['attendance_notes'])); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <div class="row mt-3">
        <div class="col-12">
            <h6 class="text-dark">سجل الأنشطة (<?php echo count($activities); ?> نشاط)</h6>
            <?php if (empty($activities)): ?>
                <div class="alert alert-secondary">لا توجد أنشطة مسجلة لهذا الشيفت</div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>الوقت</th>
                                <th>نوع النشاط</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($activities as $activity): ?>
                                <tr>
                                    <td><?php echo date('H:i:s', strtotime($activity['activity_time'])); ?></td>
                                    <td>
                                        <?php
                                        $type_labels = [
                                            'session_start' => 'بدء جلسة',
                                            'session_end' => 'إنهاء جلسة',
                                            'sale' => 'بيع',
                                            'customer_service' => 'خدمة عملاء',
                                            'maintenance' => 'صيانة',
                                            'cleaning' => 'تنظيف',
                                            'break' => 'استراحة',
                                            'other' => 'أخرى'
                                        ];
                                        $type_colors = [
                                            'session_start' => 'success',
                                            'session_end' => 'info',
                                            'sale' => 'warning',
                                            'customer_service' => 'primary',
                                            'maintenance' => 'danger',
                                            'cleaning' => 'secondary',
                                            'break' => 'light',
                                            'other' => 'dark'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $type_colors[$activity['activity_type']] ?? 'secondary'; ?>">
                                            <?php echo $type_labels[$activity['activity_type']] ?? $activity['activity_type']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($activity['activity_description']); ?></td>
                                    <td>
                                        <?php if ($activity['amount'] > 0): ?>
                                            <span class="text-success"><?php echo number_format($activity['amount'], 2); ?> ريال</span>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($activity['notes']): ?>
                                            <small class="text-muted"><?php echo htmlspecialchars($activity['notes']); ?></small>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="row mt-3">
        <div class="col-12 text-center">
            <small class="text-muted">
                تم إنشاء التقرير في: <?php echo date('Y-m-d H:i:s', strtotime($report['generated_at'])); ?>
            </small>
        </div>
    </div>
    
    <?php
    
} catch (Exception $e) {
    http_response_code(500);
    echo "حدث خطأ في جلب تفاصيل التقرير: " . $e->getMessage();
}
?>
