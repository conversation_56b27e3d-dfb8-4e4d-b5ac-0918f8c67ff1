<?php
// sidebar.php - الشريط الجانبي العائم للإدارة مع نظام الصلاحيات
$current_page = basename($_SERVER['PHP_SELF']);

// تضمين نظام الصلاحيات
require_once __DIR__ . '/admin-permissions.php';

// جلب الصفحات المسموحة للإدمن الحالي
$allowed_pages = getAllowedAdminPages();
$allowed_page_names = array_column($allowed_pages, 'page_name');

// دالة للتحقق من إمكانية عرض رابط في القائمة
function canShowMenuItem($page_name) {
    global $allowed_page_names;
    return in_array($page_name, $allowed_page_names) || hasAdminPagePermission($page_name);
}
?>

<!-- زر فتح القائمة الجانبية -->
<button class="btn btn-primary sidebar-toggle" id="sidebarToggle" type="button">
    <i class="fas fa-bars"></i>
</button>

<!-- الخلفية المظلمة -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<!-- القائمة الجانبية العائمة -->
<div class="floating-sidebar" id="floatingSidebar">
    <div class="sidebar-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-gamepad me-2"></i>
                لوحة التحكم
            </h5>
            <button class="btn btn-sm btn-outline-light sidebar-close" id="sidebarClose">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <div class="sidebar-content">
        <nav class="nav flex-column">
            <!-- لوحة التحكم الرئيسية -->
            <?php if (canShowMenuItem('dashboard')): ?>
                <a class="nav-link <?php echo $current_page == 'dashboard.php' ? 'active' : ''; ?>" href="dashboard.php">
                    <i class="fas fa-dashboard me-2"></i>
                    <span>لوحة التحكم</span>
                </a>
            <?php endif; ?>

            <!-- إدارة العملاء -->
            <?php if (canShowMenuItem('clients')): ?>
                <a class="nav-link <?php echo $current_page == 'clients.php' ? 'active' : ''; ?>" href="clients.php">
                    <i class="fas fa-users me-2"></i>
                    <span>إدارة العملاء</span>
                </a>
            <?php endif; ?>

            <!-- صلاحيات العملاء -->
            <?php if (canShowMenuItem('client_permissions')): ?>
                <a class="nav-link <?php echo $current_page == 'client_permissions.php' ? 'active' : ''; ?>" href="client_permissions.php">
                    <i class="fas fa-user-shield me-2"></i>
                    <span>صلاحيات العملاء</span>
                </a>
            <?php endif; ?>

            <!-- أجهزة العملاء -->
            <?php if (canShowMenuItem('client_devices')): ?>
                <a class="nav-link <?php echo $current_page == 'client_devices.php' ? 'active' : ''; ?>" href="client_devices.php">
                    <i class="fas fa-desktop me-2"></i>
                    <span>أجهزة العملاء</span>
                </a>
            <?php endif; ?>

            <!-- التقارير -->
            <?php if (canShowMenuItem('reports')): ?>
                <a class="nav-link <?php echo $current_page == 'reports.php' ? 'active' : ''; ?>" href="reports.php">
                    <i class="fas fa-chart-bar me-2"></i>
                    <span>التقارير</span>
                </a>
            <?php endif; ?>

            <!-- تقارير الشيفتات -->
            <?php if (canShowMenuItem('shift_reports')): ?>
                <a class="nav-link <?php echo $current_page == 'shift_reports.php' ? 'active' : ''; ?>" href="shift_reports.php">
                    <i class="fas fa-chart-line me-2"></i>
                    <span>تقارير الشيفتات</span>
                </a>
            <?php endif; ?>

            <!-- إعدادات الشيفتات -->
            <?php if (canShowMenuItem('shift_settings')): ?>
                <a class="nav-link <?php echo $current_page == 'shift_settings.php' ? 'active' : ''; ?>" href="shift_settings.php">
                    <i class="fas fa-cogs me-2"></i>
                    <span>إعدادات الشيفتات</span>
                </a>
            <?php endif; ?>

            <!-- الإعدادات -->
            <?php if (canShowMenuItem('settings')): ?>
                <a class="nav-link <?php echo $current_page == 'settings.php' ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog me-2"></i>
                    <span>الإعدادات</span>
                </a>
            <?php endif; ?>

            <!-- صلاحيات المديرين (Super Admin فقط) -->
            <?php if (canShowMenuItem('admin_permissions')): ?>
                <a class="nav-link <?php echo $current_page == 'admin_permissions.php' ? 'active' : ''; ?>" href="admin_permissions.php">
                    <i class="fas fa-user-cog me-2"></i>
                    <span>صلاحيات المديرين</span>
                </a>
            <?php endif; ?>

            <!-- إدارة المديرين (Super Admin فقط) -->
            <?php if (canShowMenuItem('admins')): ?>
                <a class="nav-link <?php echo $current_page == 'admins.php' ? 'active' : ''; ?>" href="admins.php">
                    <i class="fas fa-user-tie me-2"></i>
                    <span>إدارة المديرين</span>
                </a>
            <?php endif; ?>

            <!-- النسخ الاحتياطية -->
            <?php if (canShowMenuItem('backup')): ?>
                <a class="nav-link <?php echo $current_page == 'backup.php' ? 'active' : ''; ?>" href="backup.php">
                    <i class="fas fa-download me-2"></i>
                    <span>النسخ الاحتياطية</span>
                </a>
            <?php endif; ?>

            <!-- سجلات النظام (Super Admin فقط) -->
            <?php if (canShowMenuItem('system_logs')): ?>
                <a class="nav-link <?php echo $current_page == 'system_logs.php' ? 'active' : ''; ?>" href="system_logs.php">
                    <i class="fas fa-file-alt me-2"></i>
                    <span>سجلات النظام</span>
                </a>
            <?php endif; ?>

            <hr class="sidebar-divider">

            <div class="sidebar-user-info">
                <div class="d-flex align-items-center mb-2">
                    <div class="user-avatar me-2">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div>
                        <small class="text-muted">مرحباً</small>
                        <div class="fw-bold"><?php echo htmlspecialchars($_SESSION['admin_name'] ?? 'المدير'); ?></div>
                    </div>
                </div>
            </div>

            <a class="nav-link logout-link" href="logout.php">
                <i class="fas fa-sign-out-alt me-2"></i>
                <span>تسجيل الخروج</span>
            </a>
        </nav>
    </div>
</div>

<!-- تضمين ملف CSS للقائمة الجانبية -->
<link rel="stylesheet" href="assets/css/floating-sidebar.css">

<!-- تضمين ملف JavaScript للقائمة الجانبية -->
<script src="assets/js/floating-sidebar.js"></script>