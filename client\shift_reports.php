<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من الصلاحيات
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('shift_reports')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('view_shift_reports')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

$page_title = "تقارير الورديات";
$active_page = "shift_reports";

// معالجة الفلاتر
$start_date = $_GET['start_date'] ?? date('Y-m-01'); // بداية الشهر الحالي
$end_date = $_GET['end_date'] ?? date('Y-m-t'); // نهاية الشهر الحالي
$employee_id = $_GET['employee_id'] ?? '';
$report_type = $_GET['report_type'] ?? 'summary';

// جلب الموظفين للفلتر
$employees = $pdo->prepare("SELECT id, name, role FROM employees WHERE client_id = ? AND is_active = 1 ORDER BY name");
$employees->execute([$client_id]);
$employees_data = $employees->fetchAll(PDO::FETCH_ASSOC);

// إحصائيات عامة
$stats_query = "
    SELECT 
        COUNT(DISTINCT s.shift_id) as total_shifts,
        COUNT(DISTINCT sa.employee_id) as active_employees,
        COUNT(sa.attendance_id) as total_attendance_records,
        COUNT(CASE WHEN sa.status = 'present' THEN 1 END) as present_count,
        COUNT(CASE WHEN sa.status = 'absent' THEN 1 END) as absent_count,
        COUNT(CASE WHEN sa.status = 'late' THEN 1 END) as late_count,
        SUM(sa.actual_hours) as total_hours,
        SUM(sa.overtime_hours) as total_overtime,
        AVG(sa.late_minutes) as avg_late_minutes
    FROM shifts s
    LEFT JOIN shift_attendance sa ON s.shift_id = sa.shift_id
    WHERE s.client_id = ? AND s.shift_date BETWEEN ? AND ?
";

$params = [$client_id, $start_date, $end_date];

if ($employee_id) {
    $stats_query .= " AND sa.employee_id = ?";
    $params[] = $employee_id;
}

$stats = $pdo->prepare($stats_query);
$stats->execute($params);
$stats_data = $stats->fetch(PDO::FETCH_ASSOC);

// تقرير حسب النوع المطلوب
switch ($report_type) {
    case 'daily':
        $report_query = "
            SELECT 
                s.shift_date,
                COUNT(DISTINCT s.shift_id) as shifts_count,
                COUNT(sa.attendance_id) as attendance_count,
                COUNT(CASE WHEN sa.status = 'present' THEN 1 END) as present_count,
                COUNT(CASE WHEN sa.status = 'absent' THEN 1 END) as absent_count,
                SUM(sa.actual_hours) as total_hours,
                SUM(sa.overtime_hours) as total_overtime
            FROM shifts s
            LEFT JOIN shift_attendance sa ON s.shift_id = sa.shift_id
            WHERE s.client_id = ? AND s.shift_date BETWEEN ? AND ?
        ";
        if ($employee_id) {
            $report_query .= " AND sa.employee_id = ?";
        }
        $report_query .= " GROUP BY s.shift_date ORDER BY s.shift_date DESC";
        break;

    case 'employee':
        $report_query = "
            SELECT 
                e.id as employee_id,
                e.name as employee_name,
                e.role,
                COUNT(sa.attendance_id) as total_shifts,
                COUNT(CASE WHEN sa.status = 'present' THEN 1 END) as present_days,
                COUNT(CASE WHEN sa.status = 'absent' THEN 1 END) as absent_days,
                COUNT(CASE WHEN sa.status = 'late' THEN 1 END) as late_days,
                SUM(sa.actual_hours) as total_hours,
                SUM(sa.overtime_hours) as total_overtime,
                AVG(sa.late_minutes) as avg_late_minutes,
                (COUNT(CASE WHEN sa.status = 'present' THEN 1 END) * 100.0 / NULLIF(COUNT(sa.attendance_id), 0)) as attendance_percentage
            FROM employees e
            LEFT JOIN shift_attendance sa ON e.id = sa.employee_id
            LEFT JOIN shifts s ON sa.shift_id = s.shift_id AND s.shift_date BETWEEN ? AND ?
            WHERE e.client_id = ? AND e.is_active = 1
        ";
        $params = [$start_date, $end_date, $client_id];
        if ($employee_id) {
            $report_query .= " AND e.id = ?";
            $params[] = $employee_id;
        }
        $report_query .= " GROUP BY e.id ORDER BY e.name";
        break;

    case 'detailed':
        $report_query = "
            SELECT 
                sa.*,
                e.name as employee_name,
                e.role,
                s.shift_name,
                s.shift_date,
                s.start_time as scheduled_start,
                s.end_time as scheduled_end
            FROM shift_attendance sa
            JOIN employees e ON sa.employee_id = e.id
            JOIN shifts s ON sa.shift_id = s.shift_id
            WHERE s.client_id = ? AND s.shift_date BETWEEN ? AND ?
        ";
        if ($employee_id) {
            $report_query .= " AND sa.employee_id = ?";
        }
        $report_query .= " ORDER BY s.shift_date DESC, sa.check_in_time DESC";
        break;

    default: // summary
        $report_query = "
            SELECT 
                DATE_FORMAT(s.shift_date, '%Y-%m') as month,
                COUNT(DISTINCT s.shift_id) as shifts_count,
                COUNT(sa.attendance_id) as attendance_count,
                COUNT(CASE WHEN sa.status = 'present' THEN 1 END) as present_count,
                COUNT(CASE WHEN sa.status = 'absent' THEN 1 END) as absent_count,
                SUM(sa.actual_hours) as total_hours,
                SUM(sa.overtime_hours) as total_overtime,
                (COUNT(CASE WHEN sa.status = 'present' THEN 1 END) * 100.0 / NULLIF(COUNT(sa.attendance_id), 0)) as attendance_percentage
            FROM shifts s
            LEFT JOIN shift_attendance sa ON s.shift_id = sa.shift_id
            WHERE s.client_id = ? AND s.shift_date BETWEEN ? AND ?
        ";
        if ($employee_id) {
            $report_query .= " AND sa.employee_id = ?";
        }
        $report_query .= " GROUP BY DATE_FORMAT(s.shift_date, '%Y-%m') ORDER BY month DESC";
        break;
}

$report = $pdo->prepare($report_query);
$report->execute($params);
$report_data = $report->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-chart-line me-2"></i><?php echo $page_title; ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-outline-primary" onclick="exportReport()">
                        <i class="fas fa-download me-1"></i>تصدير التقرير
                    </button>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">فلاتر التقرير</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="employee_id" class="form-label">الموظف</label>
                            <select class="form-select" id="employee_id" name="employee_id">
                                <option value="">جميع الموظفين</option>
                                <?php foreach ($employees_data as $emp): ?>
                                    <option value="<?php echo $emp['id']; ?>" <?php echo $employee_id == $emp['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($emp['name']); ?> (<?php echo $emp['role']; ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="report_type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="report_type" name="report_type">
                                <option value="summary" <?php echo $report_type == 'summary' ? 'selected' : ''; ?>>ملخص شهري</option>
                                <option value="daily" <?php echo $report_type == 'daily' ? 'selected' : ''; ?>>تقرير يومي</option>
                                <option value="employee" <?php echo $report_type == 'employee' ? 'selected' : ''; ?>>تقرير الموظفين</option>
                                <option value="detailed" <?php echo $report_type == 'detailed' ? 'selected' : ''; ?>>تقرير مفصل</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>تطبيق الفلاتر
                            </button>
                            <a href="shift_reports.php" class="btn btn-secondary">
                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- الإحصائيات العامة -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-white bg-primary">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['total_shifts'] ?? 0; ?></h4>
                            <p class="card-text">إجمالي الورديات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-success">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['present_count'] ?? 0; ?></h4>
                            <p class="card-text">حضور</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-danger">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['absent_count'] ?? 0; ?></h4>
                            <p class="card-text">غياب</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-warning">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['late_count'] ?? 0; ?></h4>
                            <p class="card-text">تأخير</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-info">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo number_format($stats_data['total_hours'] ?? 0, 1); ?></h4>
                            <p class="card-text">ساعات العمل</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-secondary">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo number_format($stats_data['total_overtime'] ?? 0, 1); ?></h4>
                            <p class="card-text">ساعات إضافية</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التقرير -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <?php
                        $report_titles = [
                            'summary' => 'الملخص الشهري',
                            'daily' => 'التقرير اليومي',
                            'employee' => 'تقرير الموظفين',
                            'detailed' => 'التقرير المفصل'
                        ];
                        echo $report_titles[$report_type] ?? 'التقرير';
                        ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($report_data)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> لا توجد بيانات للفترة المحددة
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="reportTable">
                                <thead class="table-dark">
                                    <tr>
                                        <?php if ($report_type == 'summary'): ?>
                                            <th>الشهر</th>
                                            <th>عدد الورديات</th>
                                            <th>سجلات الحضور</th>
                                            <th>حضور</th>
                                            <th>غياب</th>
                                            <th>ساعات العمل</th>
                                            <th>ساعات إضافية</th>
                                            <th>نسبة الحضور</th>
                                        <?php elseif ($report_type == 'daily'): ?>
                                            <th>التاريخ</th>
                                            <th>عدد الورديات</th>
                                            <th>سجلات الحضور</th>
                                            <th>حضور</th>
                                            <th>غياب</th>
                                            <th>ساعات العمل</th>
                                            <th>ساعات إضافية</th>
                                        <?php elseif ($report_type == 'employee'): ?>
                                            <th>الموظف</th>
                                            <th>الدور</th>
                                            <th>إجمالي الورديات</th>
                                            <th>أيام الحضور</th>
                                            <th>أيام الغياب</th>
                                            <th>أيام التأخير</th>
                                            <th>ساعات العمل</th>
                                            <th>ساعات إضافية</th>
                                            <th>متوسط التأخير</th>
                                            <th>نسبة الحضور</th>
                                        <?php else: // detailed ?>
                                            <th>الموظف</th>
                                            <th>الوردية</th>
                                            <th>التاريخ</th>
                                            <th>وقت الحضور</th>
                                            <th>وقت الانصراف</th>
                                            <th>ساعات العمل</th>
                                            <th>ساعات إضافية</th>
                                            <th>التأخير</th>
                                            <th>الحالة</th>
                                        <?php endif; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($report_data as $row): ?>
                                    <tr>
                                        <?php if ($report_type == 'summary'): ?>
                                            <td><?php echo date('Y-m', strtotime($row['month'] . '-01')); ?></td>
                                            <td><?php echo $row['shifts_count'] ?? 0; ?></td>
                                            <td><?php echo $row['attendance_count'] ?? 0; ?></td>
                                            <td><span class="badge bg-success"><?php echo $row['present_count'] ?? 0; ?></span></td>
                                            <td><span class="badge bg-danger"><?php echo $row['absent_count'] ?? 0; ?></span></td>
                                            <td><?php echo number_format($row['total_hours'] ?? 0, 1); ?></td>
                                            <td><?php echo number_format($row['total_overtime'] ?? 0, 1); ?></td>
                                            <td>
                                                <?php
                                                $percentage = $row['attendance_percentage'] ?? 0;
                                                $class = $percentage >= 90 ? 'success' : ($percentage >= 70 ? 'warning' : 'danger');
                                                ?>
                                                <span class="badge bg-<?php echo $class; ?>"><?php echo number_format($percentage, 1); ?>%</span>
                                            </td>
                                        <?php elseif ($report_type == 'daily'): ?>
                                            <td><?php echo date('Y-m-d', strtotime($row['shift_date'])); ?></td>
                                            <td><?php echo $row['shifts_count'] ?? 0; ?></td>
                                            <td><?php echo $row['attendance_count'] ?? 0; ?></td>
                                            <td><span class="badge bg-success"><?php echo $row['present_count'] ?? 0; ?></span></td>
                                            <td><span class="badge bg-danger"><?php echo $row['absent_count'] ?? 0; ?></span></td>
                                            <td><?php echo number_format($row['total_hours'] ?? 0, 1); ?></td>
                                            <td><?php echo number_format($row['total_overtime'] ?? 0, 1); ?></td>
                                        <?php elseif ($report_type == 'employee'): ?>
                                            <td><strong><?php echo htmlspecialchars($row['employee_name']); ?></strong></td>
                                            <td><span class="badge bg-secondary"><?php echo $row['role']; ?></span></td>
                                            <td><?php echo $row['total_shifts'] ?? 0; ?></td>
                                            <td><span class="badge bg-success"><?php echo $row['present_days'] ?? 0; ?></span></td>
                                            <td><span class="badge bg-danger"><?php echo $row['absent_days'] ?? 0; ?></span></td>
                                            <td><span class="badge bg-warning"><?php echo $row['late_days'] ?? 0; ?></span></td>
                                            <td><?php echo number_format($row['total_hours'] ?? 0, 1); ?></td>
                                            <td><?php echo number_format($row['total_overtime'] ?? 0, 1); ?></td>
                                            <td><?php echo number_format($row['avg_late_minutes'] ?? 0, 0); ?> دقيقة</td>
                                            <td>
                                                <?php
                                                $percentage = $row['attendance_percentage'] ?? 0;
                                                $class = $percentage >= 90 ? 'success' : ($percentage >= 70 ? 'warning' : 'danger');
                                                ?>
                                                <span class="badge bg-<?php echo $class; ?>"><?php echo number_format($percentage, 1); ?>%</span>
                                            </td>
                                        <?php else: // detailed ?>
                                            <td>
                                                <strong><?php echo htmlspecialchars($row['employee_name']); ?></strong>
                                                <br><small class="text-muted"><?php echo $row['role']; ?></small>
                                            </td>
                                            <td><?php echo htmlspecialchars($row['shift_name']); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($row['shift_date'])); ?></td>
                                            <td>
                                                <?php if ($row['check_in_time']): ?>
                                                    <?php echo date('H:i:s', strtotime($row['check_in_time'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($row['check_out_time']): ?>
                                                    <?php echo date('H:i:s', strtotime($row['check_out_time'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo number_format($row['actual_hours'] ?? 0, 2); ?></td>
                                            <td><?php echo number_format($row['overtime_hours'] ?? 0, 2); ?></td>
                                            <td>
                                                <?php if ($row['late_minutes'] > 0): ?>
                                                    <span class="text-danger"><?php echo $row['late_minutes']; ?> دقيقة</span>
                                                <?php else: ?>
                                                    <span class="text-success">في الوقت</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_classes = [
                                                    'present' => 'bg-success',
                                                    'late' => 'bg-warning',
                                                    'early_leave' => 'bg-warning',
                                                    'overtime' => 'bg-info',
                                                    'absent' => 'bg-danger'
                                                ];
                                                $status_labels = [
                                                    'present' => 'حاضر',
                                                    'late' => 'متأخر',
                                                    'early_leave' => 'خروج مبكر',
                                                    'overtime' => 'عمل إضافي',
                                                    'absent' => 'غائب'
                                                ];
                                                ?>
                                                <span class="badge <?php echo $status_classes[$row['status']] ?? 'bg-secondary'; ?>">
                                                    <?php echo $status_labels[$row['status']] ?? $row['status']; ?>
                                                </span>
                                            </td>
                                        <?php endif; ?>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// تصدير التقرير
function exportReport() {
    // يمكن تطوير هذه الوظيفة لتصدير التقرير كـ Excel أو PDF
    const table = document.getElementById('reportTable');
    if (!table) {
        alert('لا يوجد تقرير لتصديره');
        return;
    }

    // تصدير كـ CSV بسيط
    let csv = '';
    const rows = table.querySelectorAll('tr');

    for (let i = 0; i < rows.length; i++) {
        const cols = rows[i].querySelectorAll('td, th');
        const rowData = [];

        for (let j = 0; j < cols.length; j++) {
            let cellData = cols[j].innerText.replace(/"/g, '""');
            rowData.push('"' + cellData + '"');
        }

        csv += rowData.join(',') + '\n';
    }

    // تحميل الملف
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'shift_report_<?php echo date('Y-m-d'); ?>.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تحديث تلقائي للتواريخ
document.addEventListener('DOMContentLoaded', function() {
    const reportType = document.getElementById('report_type');
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');

    reportType.addEventListener('change', function() {
        const today = new Date();

        switch (this.value) {
            case 'daily':
                // آخر 7 أيام
                const weekAgo = new Date(today);
                weekAgo.setDate(today.getDate() - 7);
                startDate.value = weekAgo.toISOString().split('T')[0];
                endDate.value = today.toISOString().split('T')[0];
                break;

            case 'summary':
                // الشهر الحالي
                const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
                const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                startDate.value = firstDay.toISOString().split('T')[0];
                endDate.value = lastDay.toISOString().split('T')[0];
                break;

            default:
                // الشهر الحالي
                const firstDayDefault = new Date(today.getFullYear(), today.getMonth(), 1);
                const lastDayDefault = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                startDate.value = firstDayDefault.toISOString().split('T')[0];
                endDate.value = lastDayDefault.toISOString().split('T')[0];
                break;
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
