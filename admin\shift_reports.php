<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/auth.php';

$page_title = "تقارير الشيفتات";
$active_page = "shift_reports";

// معالجة الفلاتر
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-7 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$client_filter = $_GET['client_id'] ?? '';
$employee_filter = $_GET['employee_id'] ?? '';

// جلب قائمة العملاء
$clients_stmt = $pdo->query("SELECT client_id, business_name FROM clients WHERE is_active = 1 ORDER BY business_name");
$clients = $clients_stmt->fetchAll();

// جلب قائمة الموظفين
$employees_query = "SELECT e.id, e.name, c.business_name FROM employees e JOIN clients c ON e.client_id = c.client_id WHERE e.is_active = 1";
if ($client_filter) {
    $employees_query .= " AND e.client_id = " . intval($client_filter);
}
$employees_query .= " ORDER BY c.business_name, e.name";
$employees_stmt = $pdo->query($employees_query);
$employees = $employees_stmt->fetchAll();

// بناء استعلام التقارير
$reports_query = "
    SELECT 
        sr.*,
        s.shift_name,
        s.shift_date,
        s.start_time,
        s.end_time,
        e.name as employee_name,
        e.role as employee_role,
        c.business_name,
        sa.check_in_time,
        sa.check_out_time,
        TIMESTAMPDIFF(HOUR, sa.check_in_time, sa.check_out_time) as actual_hours_worked
    FROM shift_reports sr
    JOIN shifts s ON sr.shift_id = s.shift_id
    JOIN employees e ON sr.employee_id = e.id
    JOIN clients c ON s.client_id = c.client_id
    LEFT JOIN shift_attendance sa ON s.shift_id = sa.shift_id AND sa.employee_id = sr.employee_id
    WHERE s.shift_date BETWEEN ? AND ?
";

$params = [$date_from, $date_to];

if ($client_filter) {
    $reports_query .= " AND s.client_id = ?";
    $params[] = $client_filter;
}

if ($employee_filter) {
    $reports_query .= " AND sr.employee_id = ?";
    $params[] = $employee_filter;
}

$reports_query .= " ORDER BY s.shift_date DESC, sr.generated_at DESC";

$reports_stmt = $pdo->prepare($reports_query);
$reports_stmt->execute($params);
$reports = $reports_stmt->fetchAll();

// حساب الإحصائيات الإجمالية
$total_shifts = count($reports);
$total_revenue = array_sum(array_column($reports, 'total_revenue'));
$total_sales = array_sum(array_column($reports, 'total_sales'));
$total_sessions = array_sum(array_column($reports, 'total_sessions'));
$avg_performance = $total_shifts > 0 ? array_sum(array_column($reports, 'performance_score')) / $total_shifts : 0;

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-chart-line"></i>
                    تقارير الشيفتات
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-success" onclick="exportReports()">
                        <i class="fas fa-download"></i>
                        تصدير التقارير
                    </button>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">فلاتر البحث</h6>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="client_id" class="form-label">المحل</label>
                            <select class="form-select" id="client_id" name="client_id" onchange="updateEmployees()">
                                <option value="">جميع المحلات</option>
                                <?php foreach ($clients as $client): ?>
                                    <option value="<?php echo $client['client_id']; ?>" 
                                            <?php echo $client_filter == $client['client_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($client['business_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="employee_id" class="form-label">الموظف</label>
                            <select class="form-select" id="employee_id" name="employee_id">
                                <option value="">جميع الموظفين</option>
                                <?php foreach ($employees as $employee): ?>
                                    <option value="<?php echo $employee['id']; ?>" 
                                            <?php echo $employee_filter == $employee['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($employee['name'] . ' - ' . $employee['business_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                            <a href="shift_reports.php" class="btn btn-secondary">
                                <i class="fas fa-refresh"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- الإحصائيات الإجمالية -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center border-primary">
                        <div class="card-body">
                            <h4 class="text-primary"><?php echo $total_shifts; ?></h4>
                            <p class="card-text">إجمالي الشيفتات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-success">
                        <div class="card-body">
                            <h4 class="text-success"><?php echo number_format($total_revenue, 2); ?></h4>
                            <p class="card-text">إجمالي الإيرادات (ريال)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-info">
                        <div class="card-body">
                            <h4 class="text-info"><?php echo number_format($total_sales, 2); ?></h4>
                            <p class="card-text">إجمالي المبيعات (ريال)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-warning">
                        <div class="card-body">
                            <h4 class="text-warning"><?php echo number_format($avg_performance, 1); ?>/10</h4>
                            <p class="card-text">متوسط الأداء</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول التقارير -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">تقارير الشيفتات (<?php echo count($reports); ?> تقرير)</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($reports)): ?>
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            لا توجد تقارير شيفتات في الفترة المحددة
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="reportsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الشيفت</th>
                                        <th>الموظف</th>
                                        <th>المحل</th>
                                        <th>ساعات العمل</th>
                                        <th>الجلسات</th>
                                        <th>الإيرادات</th>
                                        <th>المبيعات</th>
                                        <th>الأداء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($reports as $report): ?>
                                        <tr>
                                            <td><?php echo date('Y-m-d', strtotime($report['shift_date'])); ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($report['shift_name']); ?></strong><br>
                                                <small class="text-muted">
                                                    <?php echo date('H:i', strtotime($report['start_time'])); ?> - 
                                                    <?php echo date('H:i', strtotime($report['end_time'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($report['employee_name']); ?><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($report['employee_role']); ?></small>
                                            </td>
                                            <td><?php echo htmlspecialchars($report['business_name']); ?></td>
                                            <td>
                                                <?php if ($report['actual_hours_worked']): ?>
                                                    <span class="badge bg-success"><?php echo $report['actual_hours_worked']; ?>س</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">غير محدد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo $report['total_sessions']; ?></strong><br>
                                                <small class="text-muted"><?php echo $report['customers_served']; ?> عميل</small>
                                            </td>
                                            <td>
                                                <strong class="text-success"><?php echo number_format($report['total_revenue'], 2); ?></strong><br>
                                                <small class="text-muted">ريال</small>
                                            </td>
                                            <td>
                                                <strong class="text-info"><?php echo number_format($report['total_sales'], 2); ?></strong><br>
                                                <small class="text-muted"><?php echo $report['products_sold']; ?> منتج</small>
                                            </td>
                                            <td>
                                                <?php
                                                $score = $report['performance_score'];
                                                $color = $score >= 8 ? 'success' : ($score >= 6 ? 'warning' : 'danger');
                                                ?>
                                                <span class="badge bg-<?php echo $color; ?> fs-6">
                                                    <?php echo number_format($score, 1); ?>/10
                                                </span>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                                        onclick="viewReportDetails(<?php echo $report['report_id']; ?>)">
                                                    <i class="fas fa-eye"></i>
                                                    عرض
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Modal تفاصيل التقرير -->
<div class="modal fade" id="reportDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل تقرير الشيفت</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="reportDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
// تحديث قائمة الموظفين عند تغيير المحل
function updateEmployees() {
    const clientId = document.getElementById('client_id').value;
    const employeeSelect = document.getElementById('employee_id');
    
    // إعادة تعيين قائمة الموظفين
    employeeSelect.innerHTML = '<option value="">جميع الموظفين</option>';
    
    if (clientId) {
        // جلب موظفي المحل المحدد
        fetch(`api/get_client_employees.php?client_id=${clientId}`)
            .then(response => response.json())
            .then(data => {
                data.forEach(employee => {
                    const option = document.createElement('option');
                    option.value = employee.id;
                    option.textContent = employee.name;
                    employeeSelect.appendChild(option);
                });
            })
            .catch(error => console.error('خطأ في جلب الموظفين:', error));
    }
}

// عرض تفاصيل التقرير
function viewReportDetails(reportId) {
    fetch(`api/get_shift_report_details.php?report_id=${reportId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('reportDetailsContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('reportDetailsModal')).show();
        })
        .catch(error => {
            console.error('خطأ في جلب تفاصيل التقرير:', error);
            alert('حدث خطأ في جلب تفاصيل التقرير');
        });
}

// تصدير التقارير
function exportReports() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = 'api/export_shift_reports.php?' + params.toString();
}

// تهيئة DataTable
$(document).ready(function() {
    $('#reportsTable').DataTable({
        language: {
            url: '../assets/js/Arabic.json'
        },
        order: [[0, 'desc']],
        pageLength: 25,
        responsive: true
    });
});
</script>
