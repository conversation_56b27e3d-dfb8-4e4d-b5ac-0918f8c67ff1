<?php
/**
 * إضافة الجداول المفقودة لنظام الشيفتات
 * تاريخ الإنشاء: 27 سبتمبر 2025
 */

require_once 'config/database.php';

try {
    echo "<h2>إضافة الجداول المفقودة لنظام الشيفتات</h2>";
    echo "<div style='font-family: Arial; direction: rtl; text-align: right;'>";
    
    // قراءة ملف SQL
    $sql_file = 'add_missing_shift_tables.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ملف SQL غير موجود: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    if ($sql_content === false) {
        throw new Exception("فشل في قراءة ملف SQL");
    }
    
    echo "<p>✓ تم قراءة ملف SQL بنجاح</p>";
    
    // تقسيم الاستعلامات (تجاهل الـ triggers لتجنب مشاكل DELIMITER)
    $statements = explode(';', $sql_content);
    $executed = 0;
    $errors = 0;
    $skip_trigger = false;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        // تجاهل الـ triggers مؤقتاً
        if (stripos($statement, 'DELIMITER') !== false || 
            stripos($statement, 'CREATE TRIGGER') !== false ||
            $skip_trigger) {
            
            if (stripos($statement, 'DELIMITER') !== false && strpos($statement, '//') === false) {
                $skip_trigger = false;
            } else {
                $skip_trigger = true;
            }
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $executed++;
            
            // عرض نوع الاستعلام المنفذ
            if (stripos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`([^`]+)`/i', $statement, $matches);
                if (isset($matches[1])) {
                    echo "<p style='color: green;'>✓ تم إنشاء جدول: {$matches[1]}</p>";
                }
            } elseif (stripos($statement, 'CREATE VIEW') !== false) {
                preg_match('/CREATE.*?VIEW.*?`([^`]+)`/i', $statement, $matches);
                if (isset($matches[1])) {
                    echo "<p style='color: green;'>✓ تم إنشاء عرض: {$matches[1]}</p>";
                }
            } elseif (stripos($statement, 'CREATE INDEX') !== false) {
                echo "<p style='color: green;'>✓ تم إنشاء فهرس</p>";
            }
            
        } catch (PDOException $e) {
            $errors++;
            echo "<p style='color: red;'>✗ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "</p>";
            echo "<pre style='background: #f5f5f5; padding: 10px; font-size: 12px;'>" . htmlspecialchars(substr($statement, 0, 200)) . "...</pre>";
        }
    }
    
    echo "<hr>";
    echo "<h3>ملخص التنفيذ:</h3>";
    echo "<p><strong>الاستعلامات المنفذة بنجاح:</strong> $executed</p>";
    echo "<p><strong>الأخطاء:</strong> $errors</p>";
    
    if ($errors == 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ تم إضافة الجداول المفقودة بنجاح!</p>";
        
        // التحقق من الجداول المضافة
        echo "<h3>التحقق من الجداول المضافة:</h3>";
        $new_tables = ['shift_activities', 'shift_reports'];
        
        foreach ($new_tables as $table) {
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    echo "<p style='color: green;'>✓ جدول $table موجود</p>";
                    
                    // عرض عدد الأعمدة
                    $stmt = $pdo->query("DESCRIBE $table");
                    $columns = $stmt->rowCount();
                    echo "<p style='margin-right: 20px;'>عدد الأعمدة: $columns</p>";
                } else {
                    echo "<p style='color: red;'>✗ جدول $table غير موجود</p>";
                }
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ خطأ في فحص جدول $table: " . $e->getMessage() . "</p>";
            }
        }
        
        // التحقق من الـ Views
        echo "<h3>التحقق من العروض (Views):</h3>";
        try {
            $stmt = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'VIEW'");
            $views = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $expected_views = ['active_shifts_view', 'shift_reports_view'];
            foreach ($expected_views as $view) {
                if (in_array($view, $views)) {
                    echo "<p style='color: green;'>✓ عرض $view موجود</p>";
                } else {
                    echo "<p style='color: orange;'>⚠ عرض $view غير موجود</p>";
                }
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ خطأ في فحص العروض: " . $e->getMessage() . "</p>";
        }
        
        // إنشاء بيانات تجريبية
        echo "<h3>إنشاء بيانات تجريبية:</h3>";
        try {
            // التحقق من وجود شيفتات
            $stmt = $pdo->query("SELECT COUNT(*) FROM shifts");
            $shift_count = $stmt->fetchColumn();
            
            if ($shift_count > 0) {
                echo "<p style='color: green;'>✓ يوجد $shift_count شيفت في النظام</p>";
                echo "<p>يمكنك الآن استخدام نظام الشيفتات بالكامل</p>";
            } else {
                echo "<p style='color: orange;'>⚠ لا توجد شيفتات في النظام. يمكنك إنشاء شيفتات جديدة من خلال واجهة النظام</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ خطأ في فحص الشيفتات: " . $e->getMessage() . "</p>";
        }
        
        echo "<h3>الخطوات التالية:</h3>";
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
        echo "<ol>";
        echo "<li>إنشاء واجهة بدء وإنهاء الشيفت للموظفين</li>";
        echo "<li>إنشاء صفحة تقارير الشيفتات للإدمن</li>";
        echo "<li>إضافة نظام الإشعارات</li>";
        echo "<li>اختبار النظام مع بيانات حقيقية</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<p style='color: red; font-weight: bold;'>✗ حدثت أخطاء أثناء الإعداد. يرجى مراجعة الأخطاء أعلاه.</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-family: Arial; direction: rtl; text-align: right;'>";
    echo "<h2>خطأ في إضافة الجداول</h2>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

p {
    margin: 8px 0;
    padding: 5px;
}

hr {
    border: none;
    border-top: 2px solid #ecf0f1;
    margin: 20px 0;
}

pre {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    font-size: 12px;
    overflow-x: auto;
}

ol {
    text-align: right;
}

li {
    margin: 5px 0;
}
</style>
