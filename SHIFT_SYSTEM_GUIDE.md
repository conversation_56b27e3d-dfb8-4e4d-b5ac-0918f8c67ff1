# دليل نظام إدارة الشيفتات - PlayGood

## نظرة عامة
تم إنشاء نظام شامل لإدارة الشيفتات في نظام PlayGood يتيح للموظفين بدء وإنهاء الشيفتات وتسجيل الأنشطة تلقائياً، مع إنشاء تقارير مفصلة للإدارة.

## المكونات الرئيسية

### 1. قاعدة البيانات
تم إضافة الجداول التالية:

#### `shift_activities`
- تسجيل جميع أنشطة الموظف أثناء الشيفت
- أنواع الأنشطة: بدء جلسة، إنهاء جلسة، بيع، خدمة عملاء، صيانة، تنظيف، استراحة
- ربط الأنشطة بالعناصر ذات الصلة (جلسات، طلبات، إلخ)

#### `shift_reports`
- تقارير مفصلة لكل شيفت
- إحصائيات الأداء: عدد الجلسات، المبيعات، الإيرادات، العملاء المخدومين
- نقاط الأداء من 10 درجات
- ملاحظات وتقييمات

#### Views المساعدة
- `active_shifts_view`: عرض الشيفتات النشطة
- `shift_reports_view`: عرض تقارير الشيفتات مع التفاصيل

### 2. واجهة الموظف

#### صفحة التحكم في الشيفت (`client/shift_control.php`)
**المميزات:**
- عرض الشيفتات المتاحة للموظف
- بدء الشيفت بنقرة واحدة
- عرض معلومات الشيفت النشط (الوقت، المدة المنقضية)
- إنهاء الشيفت مع إمكانية إضافة ملاحظات
- إحصائيات سريعة لليوم الحالي
- عرض آخر الأنشطة

**الوظائف:**
- التحقق من عدم وجود شيفت نشط قبل البدء
- البحث عن الشيفتات المجدولة لليوم
- تسجيل أوقات الحضور والانصراف
- إنشاء تقرير تلقائي عند إنهاء الشيفت
- حساب نقاط الأداء تلقائياً

#### نظام تتبع الأنشطة التلقائي
**ملف JavaScript:** `client/assets/js/shift-tracker.js`

**المميزات:**
- مؤشر مرئي للشيفت النشط في أعلى الصفحة
- تحديث المؤقت كل دقيقة
- تسجيل الأنشطة تلقائياً:
  - بدء وإنهاء الجلسات
  - المبيعات والطلبات
  - خدمة العملاء
- واجهة برمجية لتسجيل الأنشطة اليدوية

### 3. واجهة الإدارة

#### صفحة تقارير الشيفتات (`admin/shift_reports.php`)
**المميزات:**
- فلاتر متقدمة (التاريخ، المحل، الموظف)
- إحصائيات إجمالية (عدد الشيفتات، الإيرادات، متوسط الأداء)
- جدول مفصل بجميع التقارير
- عرض تفاصيل كل تقرير في نافذة منبثقة
- إمكانية تصدير التقارير

**البيانات المعروضة:**
- معلومات الشيفت (التاريخ، الوقت، المدة)
- معلومات الموظف والمحل
- إحصائيات الأداء (الجلسات، المبيعات، العملاء)
- نقاط الأداء مع ألوان تمييزية
- سجل الأنشطة التفصيلي

### 4. APIs المساعدة

#### `client/api/shift_status_check.php`
- فحص حالة الشيفت النشط للموظف
- إرجاع معلومات الشيفت والوقت المنقضي

#### `client/api/log_shift_activity.php`
- تسجيل الأنشطة أثناء الشيفت
- دعم الأنشطة التلقائية واليدوية

#### `admin/api/get_shift_report_details.php`
- جلب تفاصيل تقرير شيفت محدد
- عرض سجل الأنشطة والإحصائيات

#### `admin/api/get_client_employees.php`
- جلب قائمة موظفي محل محدد
- لاستخدامها في فلاتر التقارير

## طريقة الاستخدام

### للموظفين:
1. تسجيل الدخول كموظف
2. الانتقال إلى "التحكم في الشيفت" من القائمة الجانبية
3. الضغط على "بدء الشيفت" للشيفت المتاح
4. العمل بشكل طبيعي - سيتم تسجيل الأنشطة تلقائياً
5. الضغط على "إنهاء الشيفت" عند الانتهاء
6. إضافة ملاحظات إن أردت

### للإدارة:
1. تسجيل الدخول كإدمن
2. الانتقال إلى "تقارير الشيفتات"
3. استخدام الفلاتر لتحديد الفترة والموظفين
4. مراجعة الإحصائيات والتقارير
5. النقر على "عرض" لرؤية تفاصيل أي تقرير

## حساب نقاط الأداء

يتم حساب نقاط الأداء من 10 درجات بناءً على:
- **5 نقاط أساسية** لجميع الموظفين
- **+1 نقطة** إذا كان عدد الجلسات > 5
- **+1 نقطة** إذا كان عدد العملاء المخدومين > 3  
- **+1 نقطة** إذا كانت المبيعات > 100 ريال
- **+1 نقطة** إذا كان متوسط مدة الجلسة > 30 دقيقة
- **+1 نقطة** إذا كان عدد المنتجات المباعة > 10

## الأنشطة المسجلة تلقائياً

- **بدء الجلسة**: عند إنشاء جلسة جديدة
- **إنهاء الجلسة**: عند إكمال جلسة
- **البيع**: عند إنشاء طلب جديد
- **خدمة العملاء**: عند التفاعل مع العملاء
- **بدء/إنهاء الشيفت**: عند بدء أو إنهاء الشيفت

## المميزات الإضافية

### مؤشر الشيفت النشط
- يظهر في أعلى يمين الصفحة للموظفين
- يعرض اسم الشيفت والوقت المنقضي
- يتحدث تلقائياً كل دقيقة
- رابط سريع لصفحة التحكم

### التحديث التلقائي
- فحص حالة الشيفت كل 5 دقائق
- تحديث المؤقت كل دقيقة
- تسجيل الأنشطة فور حدوثها

### التصميم المتجاوب
- يعمل على جميع الأجهزة
- واجهة سهلة الاستخدام
- ألوان مميزة لحالات مختلفة

## الملفات المضافة/المحدثة

### ملفات جديدة:
- `client/shift_control.php` - واجهة التحكم في الشيفت
- `admin/shift_reports.php` - تقارير الشيفتات للإدارة
- `client/assets/js/shift-tracker.js` - نظام تتبع الأنشطة
- `client/api/shift_status_check.php` - فحص حالة الشيفت
- `client/api/log_shift_activity.php` - تسجيل الأنشطة
- `admin/api/get_shift_report_details.php` - تفاصيل التقارير
- `admin/api/get_client_employees.php` - قائمة الموظفين

### ملفات محدثة:
- `client/includes/sidebar.php` - إضافة رابط التحكم في الشيفت
- `admin/includes/sidebar.php` - إضافة رابط تقارير الشيفتات  
- `client/includes/header.php` - تضمين JavaScript للموظفين

### ملفات SQL:
- `simple_shift_tables.sql` - إنشاء الجداول المطلوبة
- `add_missing_shift_tables.sql` - النسخة الكاملة مع Triggers

## الخطوات التالية المقترحة

1. **إضافة الإشعارات**: تنبيهات للإدارة عند بدء/إنهاء الشيفتات
2. **تقارير متقدمة**: رسوم بيانية وإحصائيات مرئية
3. **جدولة الشيفتات**: نظام لجدولة الشيفتات مسبقاً
4. **تقييم الأداء**: نظام تقييم شهري للموظفين
5. **التكامل مع الراتب**: ربط ساعات العمل بنظام الرواتب

## الدعم والصيانة

- جميع الأخطاء يتم تسجيلها في سجلات النظام
- النظام يتعامل مع الأخطاء بشكل آمن
- إمكانية إضافة المزيد من أنواع الأنشطة
- قابلية التوسع لإضافة مميزات جديدة

---

**تم إنشاء النظام بتاريخ:** 27 سبتمبر 2025  
**الحالة:** جاهز للاستخدام  
**المطور:** Augment Agent
