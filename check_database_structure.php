<?php
/**
 * فحص بنية قاعدة البيانات الحالية
 * تاريخ الإنشاء: 27 سبتمبر 2025
 */

require_once 'config/database.php';

try {
    echo "<h2>فحص بنية قاعدة البيانات</h2>";
    echo "<div style='font-family: Arial; direction: rtl; text-align: right;'>";
    
    // جلب جميع الجداول
    $stmt = $pdo->query('SHOW TABLES');
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>الجداول الموجودة (" . count($tables) . " جدول):</h3>";
    
    // تصنيف الجداول
    $core_tables = [];
    $shift_tables = [];
    $security_tables = [];
    $other_tables = [];
    
    foreach ($tables as $table) {
        if (strpos($table, 'shift') !== false) {
            $shift_tables[] = $table;
        } elseif (in_array($table, ['clients', 'employees', 'customers', 'devices', 'sessions', 'products', 'categories'])) {
            $core_tables[] = $table;
        } elseif (strpos($table, 'security') !== false || strpos($table, 'threat') !== false || strpos($table, 'login') !== false) {
            $security_tables[] = $table;
        } else {
            $other_tables[] = $table;
        }
    }
    
    // عرض الجداول الأساسية
    echo "<h4>الجداول الأساسية:</h4>";
    echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
    echo "<tr><th>اسم الجدول</th><th>عدد الأعمدة</th><th>عدد السجلات</th><th>الحالة</th></tr>";
    
    foreach ($core_tables as $table) {
        echo "<tr>";
        echo "<td><strong>$table</strong></td>";
        
        // عدد الأعمدة
        $stmt = $pdo->query("DESCRIBE $table");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<td>" . count($columns) . "</td>";
        
        // عدد السجلات
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<td>$count</td>";
            echo "<td style='color: green;'>✓ نشط</td>";
        } catch (Exception $e) {
            echo "<td>خطأ</td>";
            echo "<td style='color: red;'>✗ خطأ</td>";
        }
        
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض جداول الشيفتات
    echo "<h4>جداول الشيفتات الموجودة:</h4>";
    if (empty($shift_tables)) {
        echo "<p style='color: orange;'>⚠ لا توجد جداول شيفتات في قاعدة البيانات</p>";
    } else {
        echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
        echo "<tr><th>اسم الجدول</th><th>عدد الأعمدة</th><th>عدد السجلات</th></tr>";
        
        foreach ($shift_tables as $table) {
            echo "<tr>";
            echo "<td><strong>$table</strong></td>";
            
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<td>" . count($columns) . "</td>";
            
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                echo "<td>$count</td>";
            } catch (Exception $e) {
                echo "<td>خطأ</td>";
            }
            
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // عرض الجداول الأخرى
    echo "<h4>جداول أخرى:</h4>";
    echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
    echo "<tr><th>اسم الجدول</th><th>عدد الأعمدة</th><th>عدد السجلات</th><th>التوصية</th></tr>";
    
    foreach ($other_tables as $table) {
        echo "<tr>";
        echo "<td><strong>$table</strong></td>";
        
        $stmt = $pdo->query("DESCRIBE $table");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<td>" . count($columns) . "</td>";
        
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<td>$count</td>";
            
            // تحديد التوصية
            if (strpos($table, 'temp') !== false || strpos($table, 'test') !== false || strpos($table, 'backup') !== false) {
                echo "<td style='color: red;'>يمكن حذفه</td>";
            } elseif (strpos($table, 'log') !== false || strpos($table, 'audit') !== false) {
                echo "<td style='color: blue;'>مفيد للمراقبة</td>";
            } else {
                echo "<td style='color: green;'>الاحتفاظ به</td>";
            }
        } catch (Exception $e) {
            echo "<td>خطأ</td>";
            echo "<td style='color: red;'>فحص مطلوب</td>";
        }
        
        echo "</tr>";
    }
    echo "</table>";
    
    // فحص الجداول المطلوبة لنظام الشيفتات
    echo "<h3>الجداول المطلوبة لنظام الشيفتات:</h3>";
    $required_shift_tables = [
        'shifts' => 'جدول الشيفتات الأساسي',
        'shift_attendance' => 'جدول حضور الشيفتات',
        'shift_activities' => 'جدول أنشطة الشيفت',
        'shift_reports' => 'جدول تقارير الشيفتات',
        'shift_settings' => 'جدول إعدادات الشيفتات'
    ];
    
    echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
    echo "<tr><th>اسم الجدول</th><th>الوصف</th><th>الحالة</th><th>الإجراء المطلوب</th></tr>";
    
    foreach ($required_shift_tables as $table => $description) {
        echo "<tr>";
        echo "<td><strong>$table</strong></td>";
        echo "<td>$description</td>";
        
        if (in_array($table, $tables)) {
            echo "<td style='color: green;'>✓ موجود</td>";
            echo "<td>لا يوجد</td>";
        } else {
            echo "<td style='color: red;'>✗ غير موجود</td>";
            echo "<td style='color: orange;'>يجب إنشاؤه</td>";
        }
        
        echo "</tr>";
    }
    echo "</table>";
    
    // فحص العلاقات المطلوبة
    echo "<h3>فحص العلاقات والمفاتيح الخارجية:</h3>";
    
    // فحص جدول الموظفين
    if (in_array('employees', $tables)) {
        $stmt = $pdo->query("DESCRIBE employees");
        $employee_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<h4>جدول الموظفين:</h4>";
        echo "<ul>";
        
        $required_employee_columns = ['id', 'client_id', 'name', 'role', 'is_active'];
        foreach ($required_employee_columns as $col) {
            if (in_array($col, $employee_columns)) {
                echo "<li style='color: green;'>✓ $col موجود</li>";
            } else {
                echo "<li style='color: red;'>✗ $col غير موجود</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ جدول الموظفين غير موجود - مطلوب لنظام الشيفتات</p>";
    }
    
    // فحص جدول العملاء
    if (in_array('clients', $tables)) {
        echo "<p style='color: green;'>✓ جدول العملاء موجود</p>";
    } else {
        echo "<p style='color: red;'>✗ جدول العملاء غير موجود - مطلوب لنظام الشيفتات</p>";
    }
    
    // التوصيات النهائية
    echo "<h3>التوصيات:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    
    $missing_tables = array_diff(array_keys($required_shift_tables), $tables);
    if (!empty($missing_tables)) {
        echo "<p><strong>الجداول المطلوب إنشاؤها:</strong></p>";
        echo "<ul>";
        foreach ($missing_tables as $table) {
            echo "<li>$table - {$required_shift_tables[$table]}</li>";
        }
        echo "</ul>";
    }
    
    // فحص الجداول غير الضرورية
    $unnecessary_tables = [];
    foreach ($other_tables as $table) {
        if (strpos($table, 'temp') !== false || 
            strpos($table, 'test') !== false || 
            strpos($table, 'backup') !== false ||
            strpos($table, 'old') !== false) {
            $unnecessary_tables[] = $table;
        }
    }
    
    if (!empty($unnecessary_tables)) {
        echo "<p><strong>الجداول التي يمكن حذفها:</strong></p>";
        echo "<ul>";
        foreach ($unnecessary_tables as $table) {
            echo "<li style='color: red;'>$table</li>";
        }
        echo "</ul>";
    }
    
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-family: Arial; direction: rtl; text-align: right;'>";
    echo "<h2>خطأ في فحص قاعدة البيانات</h2>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3, h4 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

table {
    margin: 10px 0;
    font-size: 14px;
}

th {
    background-color: #3498db;
    color: white;
    padding: 10px;
    text-align: center;
}

td {
    padding: 8px;
    text-align: center;
    border: 1px solid #ddd;
}

ul {
    text-align: right;
}

li {
    margin: 5px 0;
}
</style>
